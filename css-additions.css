/* Table styling - consistent padding, no spaces */
table td,
table th {
    padding-left: 12px;
    padding-right: 12px;
    padding-top: 8px;
    padding-bottom: 8px;
}

/* Emoji styling - 75% size */
.emoji {
    font-size: 0.75em;
    display: inline-block;
}

/* Mermaid diagram styling - match document theme */
.mermaid {
    font-family: "Avenir Next", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
    background: transparent !important;
}

/* Gantt chart - zebra striped row groups (project phases) */
.mermaid .section0 {
    fill: transparent !important;
}

.mermaid .section1 {
    fill: rgba(0, 0, 0, 0.1) !important; /* 10% grey */
}

.mermaid .section2 {
    fill: transparent !important;
}

.mermaid .section3 {
    fill: rgba(0, 0, 0, 0.1) !important; /* 10% grey */
}

.mermaid .section4 {
    fill: transparent !important;
}

/* Widen first column for row group labels */
.mermaid .sectionTitle {
    font-weight: 600 !important;
    text-anchor: start !important;
}

/* Remove row divider lines */
.mermaid .grid .tick line {
    display: none !important;
}

.mermaid .grid line {
    stroke: none !important;
}

/* Red task bars - no border, white text on bars */
.mermaid .task,
.mermaid .active0,
.mermaid .active1,
.mermaid .active2,
.mermaid .active3 {
    fill: #dc3545 !important;
    stroke: none !important;
    border: none !important;
}

/* White text on red bars */
.mermaid .taskText {
    fill: white !important;
    font-weight: 500 !important;
}

/* Keep text outside bars as normal */
.mermaid .taskTextOutsideRight,
.mermaid .taskTextOutsideLeft {
    fill: #333 !important;
}

/* General Mermaid text styling */
.mermaid text {
    font-family: "Avenir Next", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
}

/* Remove any default shadows or effects */
.mermaid * {
    filter: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
}
