Off-boarding
Github repo access
We can provide an export of the code, including the git history. We think this will be a better
solution as the Gitlab repository will no longer be available after the support contract ends.
See attached zip folder
Any other required credentials/access details for other
areas/methods of access, eg. monitoring, alerting, other 3rd party
tools or integrations
<PERSON> has access to all of the infrastructure and any other tools being used. As we only
have limited access to these, we wouldn't be able to invite you to the account. We can
provide the credentials we use if required though if <PERSON> doesn't have them.
Details of the deployment process including version control/branch
management strategy
Deployment is currently done as part of our Gitlab CI pipeline. It is using a CI server owned
by Rareloop and will not be available after the support contract ends.
The production server is an AWS Elastic Beanstalk instance, and we use the AWS CLI
tool to deploy a ZIP file to the instance. There is a file at the root which is
responsible for generating the ZIP file that gets deployed. Below are the scripts that our CI
server runs as part of deployment (some of the lines may not be relevant if you're deploying
via a different method):
Step 1: Build assets - using docker image:
Step 2: Create ZIP file - using docker image
Step 3: Deploy ZIP file - using docker image
We can provide the AWS environment variables securely if you are unable to find these from
AWS.
Details of any backup process in place
<PERSON> is generally in-charge of the infrastructure and any backups are done via AWS.
Details of access control process and any specific security
measures in place
All access control is managed by <PERSON>. We have limited access to AWS. After taking on
this project, we recommend:
- removing our access to the AWS account (either by removing the user or changing the
password)
- removing any SSH key/pairs on the AWS account (or IAM permissions?) to revoke our
SSH access
Details of any monitoring/alerting
Again this is all handled by Ashley. We were using our own uptime monitoring tool that
monitors:
- Uptime
- Performance
- SSL certificates
- Domain expiry
- DNS
This monitor will be removed after the support contract has ended. However you should be
able to set-up something similar using a tool like https://uptimerobot.com/.
Updating the web server configuration - is there a process for this?
There is no set process for this. It is risky, especially as there is no staging environment to
test beforehand. If any changes were needed, we would generally ensure we knew and
understood all the moving parts, identify any risk, take backups/server snapshots, run
through everything with Ashley beforehand etc.
Details of any recent changes made
List of changes made in 2024 (further details in Asana)
Pushing gclid codes through on enquires
Removing deleted PDF’s from the download folder
Update email address for newsletter sign ups
New ‘Make an Enquiry’ page
Improvements to Homepage Carousel
Fix to code in Prototype JS causing issues with maps
Update Twitter Logo
Changes to Start Planning now page
Update Google Tag Manager containers
You should be able to use the Git history to see what/how these were done technically. We
followed our usual git workflow and conventions so commits etc should be descriptive.
Any risks or areas needing attention
Bots have been hitting the website more frequently. We did some work to block some, which
seems to have helped. But we also looked at adding Cloudflare in-front of the server to help.
However Ashley and us both agree that the best way forward is to keep things as they are
right now and monitor for future attacks. The reason for this is due to the complexity and risk
associated with adding Cloudflare for DDoS protection. It would be complicated and risky to
migrate all the DNS records over to Cloudflare.
Also, the images that are being used are no longer supported by Amazon AWS and there's a
risk that Amazon removes them completely within <2 years. This includes the database and
PHP. It was our recommendation at the time to focus on re-building the website rather than
trying to upgrade the current system to newer versions etc.
It's also really important that the "debug" config variable in
However, when doing local development it's likely you want to set this to . Just ensure it
remains as
on the production server. Our CI process had a lint check to validate this.
Also note that as well as the main site, which uses CakePHP - there is also a blog that uses
WordPress
As the project is old and the version of CakePHP is no longer supported, there are a couple
of forks of the repository:
- https://github.com/mozmorris/cakephp - this is the original developer who forked the
cahephp project
- https://github.com/tomb1n0/cakephp - we forked to ensure we always
had a copy of the working CakePHP project (e.g. in-case mozmorris deleted his fork).
We would recommend you take a fork of tomb1n0/cakephp and update the
file to use your repository.
Any other documentation available for the site or related processes
or policies
I'm adding our internal documentation below for reference. Some of this was written before
we moved the deploy to the CI server:
Infrastructure
Application setup on Amazon Elastic Beanstalk.
Beanstalk is essentially a way of deploying an application that gets deployed to multiple
virtual machines, it will spin up and down instances as required.
● Minimum of 1 Instance, Maximum of 2. Autoscales when CPU usage hits 70%
● Environment Variables for database etc, are handled at the Beanstalk level (can edit
these in the beanstalk GUI)
● PHP 7.2
● Composer 1
● Node JS 0.12
○ NPM, Bower & Gulp for building CSS & JS
● Cake PHP 1.2 (Fork created by Moz to support PHP 7.2)
○ I have forked it here: GitHub - tomb1n0/cakephp at php7.2-migration
● Amazon RDS for the Database
● Amazon EFS storage mounted to
scripts in the repo
● Assets are served by Amazons Cloudfront CDN.
done by some
○
○
○
○
○
CSS is served by cloud front on assets.bon-voyage.co.uk
JS appears to also be served by assets.bon-voyage.co.uk
Images served by resources.bon-voyage.co.uk
This is done by syncing the directory on the server with S3 on a
Cron Job (runs every minute) (!)
It's unclear to me at this stage if the main site and blog use cloudfront, or if
the blog just uses the regular wordpress uploads.
Deploying
This is my current understanding on what we'd have to do to deploy to Beanstalk:
We can use the Elastic Beanstalk CLI to deploy:
●
○
○
○
will configure the repository for use with beanstalk.
This will ask for an AWS access key & Token which can be retrieved by
looking at the beanstalk applications environment variables mentioned above
Wherever this is being run from will need a copy of the AWS SSH Key pair
Once this is done you can then do things like to ssh into beanstalk
EC2 instances etc
● run
● run
○
○
○
in the repo to use docker to build JS CSS etc
to zip up the repo into an application.zip file.
this will do to create a zip file
of the repo in its current state. Note that because this uses Git, anything in the
.gitignore will not make its way up to the server
As built CSS and JS is in .gitignore, it will then manually add these into the zip
file
it will then do a
us to Beanstalk
and deploy the application.zip for
Local development
In the root of the project there is a n which can be used to bring up a docker
container to run the website in. There's further instructions in the
of the project.
file at the root
