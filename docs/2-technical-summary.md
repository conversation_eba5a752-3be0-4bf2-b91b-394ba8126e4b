# Bon Voyage Migration - Technical Summary

## Table of Contents

1. [Technical Architecture Overview](#technical-architecture-overview)
2. [Workstream A: AWS Infrastructure Implementation](#workstream-a-aws-infrastructure-implementation)
3. [Workstream B: WordPress Development Implementation](#workstream-b-wordpress-development-implementation)
4. [Database Migration Analysis](#database-migration-analysis)
5. [CakePHP Migration Feasibility Analysis](#cakephp-migration-feasibility-analysis)
6. [Blog Migration: Bedrock/Timber to Vanilla WordPress](#blog-migration-bedrocktimber-to-vanilla-wordpress)
7. [Project Management Framework](#project-management-framework)
8. [Testing and Quality Assurance](#testing-and-quality-assurance)

---

<div style="page-break-before: always;"></div>

## Technical Architecture Overview

### **Current Architecture**
- **CakePHP 1.2.12**: Legacy framework with custom components
- **PHP 7.2**: End-of-life platform on Amazon Linux
- **MariaDB 10.11**: MySQL-compatible database with complex schema
- **WordPress Blog**: Bedrock/Timber implementation at /blog
- **AWS Infrastructure**: Elastic Beanstalk with EFS and S3 storage

### **Target Architecture**
- **WordPress + ACF**: Modern content management with custom fields
- **PHP 8.1**: Current LTS version with security support
- **MySQL 8.0**: Modern database with performance improvements
- **Vanilla WordPress**: Standard implementation for easier maintenance
- **Modernized AWS**: Updated Elastic Beanstalk with optimized configuration

### **Migration Approach**
- **Database**: 85% automated conversion with validation scripts
- **Content**: Custom post types with ACF field mapping
- **Forms**: Gravity Forms with CRM integration
- **Media**: S3 + EFS hybrid storage for WordPress uploads
- **Navigation**: Shared menu system between main site and blog

<div style="page-break-before: always;"></div>

## Workstream A: AWS Infrastructure Implementation

### **Detailed Timeline**

```mermaid
gantt
    title AWS Infrastructure Workstream
    dateFormat X
    axisFormat Day %d
    
    section Phase 1: Assessment
    Analyze Current Infrastructure    :assess1, 1, 1d
    Document VPC and Security        :assess2, after assess1, 1d
    Plan Target Architecture         :assess3, after assess2, 1d
    
    section Phase 2: Environment Setup
    Create RDS MySQL Instance        :setup1, after assess3, 1d
    Configure S3 Media Storage       :setup2, after setup1, 1d
    Setup Elastic Beanstalk          :setup3, after setup2, 1d
    
    section Phase 3: Database Migration
    Export Current Database          :db1, after setup3, 1d
    Schema Transformation           :db2, after db1, 1d
    Data Validation                 :db3, after db2, 1d
    
    section Phase 4: Deployment
    CI/CD Pipeline Setup            :deploy1, after db3, 1d
    Monitoring Configuration        :deploy2, after deploy1, 1d
    
    section Phase 5: Go-Live
    Production Deployment           :golive1, after deploy2, 1d
    Post-Launch Support            :golive2, after golive1, 6d
```

### **Phase 1: Infrastructure Assessment and Planning (Days 1-3)**

#### **Current Environment Analysis**
```yaml
# Existing Elastic Beanstalk Configuration
Environment: Bvwww-env-1
Platform: PHP 7.2 on Amazon Linux (⚠️ EOL)
Auto Scaling: 1-2 instances, scales at 70% CPU
Region: eu-west-2 (London)

# Network Configuration
VPC: vpc-3f2dae57
Subnets:
  - subnet-51b92838 (AZ A)
  - subnet-756dbf0f (AZ B)
  - subnet-de37d792 (AZ C)
Security Group: sg-0e826ec64a13a2a54

# Storage Configuration
EFS: Mounted to /resources for shared files
S3 Bucket: bv-www-resources (synced via cron)
CloudFront CDN:
  - assets.bon-voyage.co.uk (CSS/JS)
  - resources.bon-voyage.co.uk (Images)
```

#### **Target Infrastructure Design**
```yaml
# New Production Environment
Platform: PHP 8.1 on Amazon Linux 2023
Database: RDS MySQL 8.0 (Multi-AZ)
Storage: S3 + EFS hybrid for WordPress media
CDN: CloudFront with optimized caching
Monitoring: CloudWatch + AWS X-Ray
Backup: Automated RDS snapshots + S3 versioning
```

#### **Tasks and Deliverables**
1. **Infrastructure Audit**
   - Document all current AWS resources and configurations
   - Analyze performance metrics and bottlenecks
   - Identify security vulnerabilities and compliance gaps
   - **Deliverable**: Complete infrastructure inventory and assessment report

2. **Architecture Planning**
   - Design PHP 8.1 Elastic Beanstalk environment
   - Plan RDS MySQL 8.0 migration strategy
   - Design S3 + EFS hybrid storage for WordPress media
   - **Deliverable**: Target architecture specification with migration plan

### **Phase 2: Environment Setup and Configuration (Days 4-6)**

#### **RDS Database Configuration**
```bash
# Create RDS MySQL 8.0 instance
aws rds create-db-instance \
  --db-instance-identifier bon-voyage-prod \
  --db-instance-class db.t3.small \
  --engine mysql \
  --engine-version 8.0.35 \
  --allocated-storage 50 \
  --storage-type gp3 \
  --storage-encrypted \
  --master-username admin \
  --master-user-password [secure-password] \
  --vpc-security-group-ids sg-database-new \
  --db-subnet-group-name bon-voyage-subnet-group \
  --backup-retention-period 7 \
  --multi-az \
  --deletion-protection
```

#### **WordPress Media Storage: S3 + EFS Configuration**
```bash
# Create S3 bucket for WordPress media
aws s3 mb s3://bon-voyage-wp-media --region eu-west-2

# Configure bucket policy for public read access
aws s3api put-bucket-policy --bucket bon-voyage-wp-media --policy '{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "PublicReadGetObject",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::bon-voyage-wp-media/*"
    }
  ]
}'

# Enable versioning for backup protection
aws s3api put-bucket-versioning \
  --bucket bon-voyage-wp-media \
  --versioning-configuration Status=Enabled
```

#### **S3FS Configuration for EBS Mounting**
```bash
# .ebextensions/s3fs-setup.config
packages:
  yum:
    fuse: []
    fuse-devel: []
    gcc-c++: []

commands:
  01_install_s3fs:
    command: |
      cd /tmp
      git clone https://github.com/s3fs-fuse/s3fs-fuse.git
      cd s3fs-fuse
      ./autogen.sh && ./configure && make && make install
      
  02_create_mount_point:
    command: |
      mkdir -p /var/www/html/wp-content/uploads
      chown www-data:www-data /var/www/html/wp-content/uploads
      
  03_mount_s3_bucket:
    command: |
      s3fs bon-voyage-wp-media /var/www/html/wp-content/uploads \
        -o passwd_file=/etc/passwd-s3fs \
        -o url=https://s3.eu-west-2.amazonaws.com \
        -o use_cache=/tmp/s3fs-cache \
        -o allow_other -o uid=33 -o gid=33
```

#### **Elastic Beanstalk Environment Configuration**
```yaml
# .ebextensions/environment.config
option_settings:
  aws:elasticbeanstalk:application:environment:
    RDS_HOSTNAME: !GetAtt Database.Endpoint.Address
    RDS_PORT: 3306
    RDS_DB_NAME: bon_voyage_wp
    RDS_USERNAME: admin
    WP_ENV: production
    WP_HOME: https://www.bon-voyage.co.uk
    
  aws:autoscaling:launchconfiguration:
    InstanceType: t3.medium
    SecurityGroups: sg-application-new
    
  aws:autoscaling:asg:
    MinSize: 2
    MaxSize: 6
    
  aws:elasticbeanstalk:environment:proxy:staticfiles:
    /wp-content/uploads: wp-content/uploads
```

### **Phase 3: Database Migration and Validation (Days 7-8)**

#### **Migration Strategy**
```bash
#!/bin/bash
# database-migration.sh - Complete database migration script

echo "Starting database migration..."

# 1. Create full backup of current database
mysqldump --single-transaction --routines --triggers \
  --host=current-db-host \
  --user=current-user \
  --password=current-password \
  bon_voyage > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. Schema compatibility adjustments
sed 's/ENGINE=MyISAM/ENGINE=InnoDB/g' backup_*.sql | \
mysql --host=$RDS_HOSTNAME --user=$RDS_USERNAME --password=$RDS_PASSWORD bon_voyage_wp

# 3. Validate data integrity
php validate-migration.php --source=old-db --target=new-db
```

#### **Data Validation Script**
```php
<?php
// validate-migration.php - Comprehensive data validation

class MigrationValidator {
    
    public function validateMigration() {
        $this->validateRecordCounts();
        $this->validateDataIntegrity();
        $this->validateRelationships();
        $this->validateImageReferences();
    }
    
    private function validateRecordCounts() {
        $tables = [
            'destinations' => 589,
            'accommodation' => 671,
            'activities' => 719,
            'images' => 7887,
            'content_blocks' => 4986
        ];
        
        foreach ($tables as $table => $expectedCount) {
            $actualCount = $this->getRecordCount($table);
            if ($actualCount !== $expectedCount) {
                throw new Exception("Record count mismatch in {$table}");
            }
            echo "✓ {$table}: {$actualCount} records validated\n";
        }
    }
    
    private function validateRelationships() {
        // Validate foreign key relationships
        $relationships = [
            'accommodation_destinations' => [
                'accommodation_id' => 'accommodation.id',
                'destination_id' => 'destinations.id'
            ],
            'activities_destinations' => [
                'activity_id' => 'activities.id',
                'destination_id' => 'destinations.id'
            ]
        ];
        
        foreach ($relationships as $table => $foreignKeys) {
            $this->validateForeignKeys($table, $foreignKeys);
        }
    }
}
```

### **Phase 4: Deployment Pipeline and Monitoring (Days 9-10)**

#### **CI/CD Pipeline Configuration**
```yaml
# .github/workflows/deploy.yml
name: Deploy to AWS Elastic Beanstalk

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup PHP 8.1
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.1'
        extensions: mysqli, pdo_mysql, gd, zip
        
    - name: Install Composer dependencies
      run: composer install --no-dev --optimize-autoloader
      
    - name: Build assets
      run: |
        npm install
        npm run build
        
    - name: Deploy to Elastic Beanstalk
      uses: einaregilsson/beanstalk-deploy@v21
      with:
        aws_access_key: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws_secret_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        application_name: bon-voyage-wp
        environment_name: production
        region: eu-west-2
```

#### **Monitoring Configuration**
```yaml
# cloudwatch-alarms.yml
Resources:
  HighCPUAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: BonVoyage-HighCPU
      MetricName: CPUUtilization
      Namespace: AWS/EC2
      Statistic: Average
      Period: 300
      Threshold: 80
      ComparisonOperator: GreaterThanThreshold
      
  DatabaseConnectionAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: BonVoyage-HighDBConnections
      MetricName: DatabaseConnections
      Namespace: AWS/RDS
      Threshold: 40
      ComparisonOperator: GreaterThanThreshold
```

<div style="page-break-before: always;"></div>

## Workstream B: WordPress Development Implementation

### **Detailed Timeline**

```mermaid
gantt
    title WordPress Development Workstream
    dateFormat X
    axisFormat Day %d
    
    section Phase 1: Analysis
    CakePHP Codebase Analysis        :analysis1, 1, 2d
    Database Schema Mapping          :analysis2, after analysis1, 1d
    WordPress Architecture Design    :analysis3, after analysis2, 1d
    
    section Phase 2: WordPress Setup
    WordPress Installation           :setup1, after analysis3, 1d
    Custom Theme Development         :setup2, after setup1, 2d
    ACF Configuration               :setup3, after setup2, 1d
    
    section Phase 3: Content Migration
    Migration Script Development     :migrate1, after setup3, 2d
    Content Migration Execution      :migrate2, after migrate1, 2d
    
    section Phase 4: Integration
    Third-party Integrations        :integrate1, after migrate2, 2d
    Testing and Optimization        :integrate2, after integrate1, 1d
    
    section Phase 5: Final Testing
    User Acceptance Testing         :test1, after integrate2, 2d
    Launch Preparation             :test2, after test1, 1d
```

### **Phase 1: CakePHP Analysis and WordPress Architecture Planning (Days 1-3)**

#### **CakePHP System Analysis**
```php
// Current CakePHP 1.2.12 structure analysis
app/
├── controllers/
│   ├── destinations_controller.php    # 589 destinations with hierarchy
│   ├── accommodation_controller.php   # 671 hotels with relationships
│   ├── activities_controller.php      # 719 activities with destinations
│   ├── travel_plans_controller.php    # Lead generation forms
│   └── pages_controller.php          # CMS pages with tree structure
├── models/
│   ├── destination.php               # Tree behavior, associations
│   ├── accommodation.php             # HABTM with destinations
│   ├── activity.php                  # Many-to-many relationships
│   └── travel_plan.php              # Form handling with CRM
├── components/
│   ├── navigation_component.php      # Mega menu and mobile menu
│   ├── filter_component.php         # Search and filtering
│   └── history_component.php        # Breadcrumb navigation
└── helpers/
    ├── app_helper.php               # Custom view helpers
    ├── image_helper.php             # Image processing
    └── webadmin_helper.php          # Admin interface helpers
```

#### **WordPress Architecture Design**
```php
// Target WordPress structure
wp-content/
├── themes/bonvoyage/
│   ├── functions.php                 # Theme functionality
│   ├── single-destination.php       # Destination detail pages
│   ├── single-accommodation.php     # Hotel detail pages
│   ├── archive-destination.php      # Destination listings
│   └── page-travel-plans.php       # Travel planning form
├── plugins/
│   ├── advanced-custom-fields-pro/  # Custom field management
│   ├── gravityforms/                # Form handling
│   └── bon-voyage-functionality/    # Custom business logic
└── mu-plugins/
    └── bon-voyage-post-types.php    # Custom post type registration
```

#### **Custom Post Types Design**
```php
// Custom post types for WordPress migration
function register_bon_voyage_post_types() {

    // Destinations (hierarchical)
    register_post_type('destination', [
        'public' => true,
        'hierarchical' => true,
        'supports' => ['title', 'editor', 'thumbnail', 'page-attributes'],
        'has_archive' => true,
        'rewrite' => ['slug' => 'destinations'],
        'menu_icon' => 'dashicons-location-alt'
    ]);

    // Accommodation
    register_post_type('accommodation', [
        'public' => true,
        'supports' => ['title', 'editor', 'thumbnail'],
        'has_archive' => true,
        'rewrite' => ['slug' => 'accommodation'],
        'menu_icon' => 'dashicons-building'
    ]);

    // Activities
    register_post_type('activity', [
        'public' => true,
        'supports' => ['title', 'editor', 'thumbnail'],
        'has_archive' => true,
        'rewrite' => ['slug' => 'activities'],
        'menu_icon' => 'dashicons-tickets-alt'
    ]);

    // Holiday Types
    register_post_type('holiday_type', [
        'public' => true,
        'supports' => ['title', 'editor', 'thumbnail'],
        'has_archive' => true,
        'rewrite' => ['slug' => 'holiday-types'],
        'menu_icon' => 'dashicons-palmtree'
    ]);

    // Itineraries
    register_post_type('itinerary', [
        'public' => true,
        'supports' => ['title', 'editor', 'thumbnail'],
        'has_archive' => true,
        'rewrite' => ['slug' => 'itineraries'],
        'menu_icon' => 'dashicons-calendar-alt'
    ]);
}
add_action('init', 'register_bon_voyage_post_types');
```

#### **ACF Field Groups Planning**
```php
// ACF field groups for complex data structures
$destination_fields = [
    'key' => 'group_destinations',
    'title' => 'Destination Information',
    'fields' => [
        ['key' => 'latitude', 'name' => 'latitude', 'type' => 'number'],
        ['key' => 'longitude', 'name' => 'longitude', 'type' => 'number'],
        ['key' => 'meta_description', 'name' => 'meta_description', 'type' => 'textarea'],
        ['key' => 'youtube_playlist_id', 'name' => 'youtube_playlist_id', 'type' => 'text'],
        ['key' => 'from_price', 'name' => 'from_price', 'type' => 'number'],
        ['key' => 'currency', 'name' => 'currency', 'type' => 'select'],
        ['key' => 'content_blocks', 'name' => 'content_blocks', 'type' => 'flexible_content']
    ],
    'location' => [
        [['param' => 'post_type', 'operator' => '==', 'value' => 'destination']]
    ]
];

$accommodation_fields = [
    'key' => 'group_accommodation',
    'title' => 'Accommodation Details',
    'fields' => [
        ['key' => 'star_rating', 'name' => 'star_rating', 'type' => 'number'],
        ['key' => 'from_price', 'name' => 'from_price', 'type' => 'number'],
        ['key' => 'destinations', 'name' => 'destinations', 'type' => 'relationship'],
        ['key' => 'holiday_types', 'name' => 'holiday_types', 'type' => 'relationship'],
        ['key' => 'image_gallery', 'name' => 'image_gallery', 'type' => 'gallery']
    ],
    'location' => [
        [['param' => 'post_type', 'operator' => '==', 'value' => 'accommodation']]
    ]
];
```

### **Phase 2: WordPress Foundation Setup (Days 4-6)**

#### **WordPress Configuration**
```php
// wp-config.php production configuration
define('WP_ENV', 'production');
define('WP_HOME', 'https://www.bon-voyage.co.uk');
define('WP_SITEURL', 'https://www.bon-voyage.co.uk/wp');

// Database configuration (from AWS RDS)
define('DB_NAME', getenv('RDS_DB_NAME'));
define('DB_USER', getenv('RDS_USERNAME'));
define('DB_PASSWORD', getenv('RDS_PASSWORD'));
define('DB_HOST', getenv('RDS_HOSTNAME'));

// WordPress security keys (generated)
define('AUTH_KEY', getenv('AUTH_KEY'));
define('SECURE_AUTH_KEY', getenv('SECURE_AUTH_KEY'));
define('LOGGED_IN_KEY', getenv('LOGGED_IN_KEY'));
define('NONCE_KEY', getenv('NONCE_KEY'));

// Performance and security
define('WP_DEBUG', false);
define('WP_DEBUG_LOG', true);
define('WP_MEMORY_LIMIT', '256M');
define('UPLOADS', 'wp-content/uploads');
```

#### **Custom Theme Development**
```php
// functions.php - Theme setup and functionality
function bonvoyage_theme_setup() {
    // Theme support
    add_theme_support('post-thumbnails');
    add_theme_support('html5', ['search-form', 'comment-form']);
    add_theme_support('title-tag');

    // Image sizes for different content types
    add_image_size('destination-hero', 1200, 600, true);
    add_image_size('accommodation-thumb', 400, 300, true);
    add_image_size('activity-card', 350, 250, true);

    // Navigation menus
    register_nav_menus([
        'primary' => 'Primary Navigation',
        'footer' => 'Footer Navigation',
        'mobile' => 'Mobile Navigation'
    ]);
}
add_action('after_setup_theme', 'bonvoyage_theme_setup');

// Enqueue styles and scripts
function bonvoyage_enqueue_assets() {
    // Main stylesheet
    wp_enqueue_style('bonvoyage-style', get_stylesheet_uri(), [], '1.0.0');

    // Navigation CSS (shared with main site)
    wp_enqueue_style('navigation-css',
        get_template_directory_uri() . '/assets/css/navigation.css');

    // Main JavaScript
    wp_enqueue_script('bonvoyage-main',
        get_template_directory_uri() . '/assets/js/main.js',
        ['jquery'], '1.0.0', true);

    // Localize script for AJAX
    wp_localize_script('bonvoyage-main', 'bonvoyage_ajax', [
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('bonvoyage_nonce')
    ]);
}
add_action('wp_enqueue_scripts', 'bonvoyage_enqueue_assets');
```

#### **Template Development**
```php
// single-destination.php - Destination detail page template
get_header(); ?>

<div class="page-content-body">
    <div class="page-content-body__content">
        <?php while (have_posts()) : the_post(); ?>
            <div class="destination-hero">
                <?php if (has_post_thumbnail()) : ?>
                    <?php the_post_thumbnail('destination-hero',
                        ['class' => 'destination-hero__image']); ?>
                <?php endif; ?>

                <div class="destination-hero__content">
                    <h1 class="destination-hero__title"><?php the_title(); ?></h1>
                    <?php if (get_field('from_price')) : ?>
                        <div class="destination-hero__price">
                            From <?php echo get_field('currency', 'option'); ?><?php the_field('from_price'); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="destination-content">
                <div class="destination-content__main">
                    <?php the_content(); ?>

                    <?php if (have_rows('content_blocks')) : ?>
                        <div class="content-blocks">
                            <?php while (have_rows('content_blocks')) : the_row(); ?>
                                <?php get_template_part('template-parts/content-block',
                                    get_row_layout()); ?>
                            <?php endwhile; ?>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="destination-content__sidebar">
                    <?php get_template_part('template-parts/destination-sidebar'); ?>
                </div>
            </div>

        <?php endwhile; ?>
    </div>
</div>

<?php get_footer(); ?>
```

### **Phase 3: Content Migration Development (Days 7-10)**

#### **Migration Script Architecture**
```php
<?php
// BonVoyageMigrationScript.php - Main migration controller

class BonVoyageMigrationScript {

    private $sourceDB;
    private $targetDB;
    private $mappings = [];

    public function __construct() {
        $this->sourceDB = new PDO("mysql:host={$_ENV['OLD_DB_HOST']};dbname={$_ENV['OLD_DB_NAME']}",
                                  $_ENV['OLD_DB_USER'], $_ENV['OLD_DB_PASS']);
        $this->targetDB = new PDO("mysql:host={$_ENV['RDS_HOSTNAME']};dbname={$_ENV['RDS_DB_NAME']}",
                                  $_ENV['RDS_USERNAME'], $_ENV['RDS_PASSWORD']);
    }

    public function migrate() {
        $this->logProgress("Starting Bon Voyage migration...");

        try {
            $this->setupWordPressStructure();
            $this->migrateUsers();
            $this->migrateImages();
            $this->migrateDestinations();
            $this->migrateAccommodation();
            $this->migrateActivities();
            $this->migrateHolidayTypes();
            $this->migrateItineraries();
            $this->migratePages();
            $this->migrateSpotlights();
            $this->migrateTravelPlans();
            $this->setupRelationships();
            $this->migrateContentBlocks();
            $this->validateMigration();

            $this->logProgress("Migration completed successfully!");

        } catch (Exception $e) {
            $this->logError("Migration failed: " . $e->getMessage());
            throw $e;
        }
    }

    private function migrateDestinations() {
        $this->logProgress("Migrating destinations...");

        // Get destinations ordered by tree structure (lft ASC)
        $stmt = $this->sourceDB->query("
            SELECT * FROM destinations
            WHERE published = 1
            ORDER BY lft ASC
        ");

        $destinations = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $migrated = 0;

        foreach ($destinations as $dest) {
            $post_id = wp_insert_post([
                'post_title' => $dest['name'],
                'post_name' => $dest['slug'],
                'post_content' => $dest['summary'] ?: '',
                'post_excerpt' => $dest['meta_description'] ?: '',
                'post_type' => 'destination',
                'post_status' => 'publish',
                'post_parent' => $this->getWordPressParent($dest['parent_id']),
                'menu_order' => $dest['lft'] // Preserve tree order
            ]);

            if ($post_id) {
                // Migrate ACF fields
                update_field('latitude', $dest['latitude'], $post_id);
                update_field('longitude', $dest['longitude'], $post_id);
                update_field('meta_description', $dest['meta_description'], $post_id);
                update_field('youtube_playlist_id', $dest['youtube_playlist_id'], $post_id);
                update_field('from_price', $dest['from_price'], $post_id);
                update_field('currency', $dest['currency'], $post_id);

                // Set featured image
                if ($dest['main_image_id']) {
                    $attachment_id = $this->mappings['images'][$dest['main_image_id']] ?? null;
                    if ($attachment_id) {
                        set_post_thumbnail($post_id, $attachment_id);
                    }
                }

                // Store mapping for relationships
                $this->mappings['destinations'][$dest['id']] = $post_id;
                $migrated++;
            }
        }

        $this->logProgress("Migrated {$migrated} destinations");
    }

    private function migrateImages() {
        $this->logProgress("Migrating images...");

        $stmt = $this->sourceDB->query("SELECT * FROM images WHERE published = 1");
        $images = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $migrated = 0;

        foreach ($images as $image) {
            // Copy physical file
            $oldPath = "/old/app/webroot/img/{$image['id']}.{$image['extension']}";
            $uploadDir = wp_upload_dir();
            $newPath = $uploadDir['path'] . "/{$image['id']}.{$image['extension']}";

            if (file_exists($oldPath)) {
                copy($oldPath, $newPath);

                // Create WordPress attachment
                $attachment_id = wp_insert_attachment([
                    'post_title' => $image['alt'] ?: 'Image ' . $image['id'],
                    'post_content' => '',
                    'post_status' => 'inherit',
                    'post_mime_type' => 'image/' . $image['extension']
                ], $newPath);

                if ($attachment_id) {
                    // Generate metadata
                    require_once(ABSPATH . 'wp-admin/includes/image.php');
                    $metadata = wp_generate_attachment_metadata($attachment_id, $newPath);
                    wp_update_attachment_metadata($attachment_id, $metadata);

                    // Store mapping
                    $this->mappings['images'][$image['id']] = $attachment_id;
                    $migrated++;
                }
            }
        }

        $this->logProgress("Migrated {$migrated} images");
    }

    private function migrateContentBlocks() {
        $this->logProgress("Migrating content blocks...");

        $stmt = $this->sourceDB->query("
            SELECT * FROM content_blocks
            ORDER BY model, modelid, `order`
        ");

        $blocks = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $grouped = [];

        // Group blocks by model and model ID
        foreach ($blocks as $block) {
            $grouped[$block['model']][$block['modelid']][] = $block;
        }

        foreach ($grouped as $model => $modelBlocks) {
            foreach ($modelBlocks as $modelId => $blocks) {
                $postId = $this->getWordPressPostId($model, $modelId);

                if ($postId) {
                    $flexibleContent = [];

                    foreach ($blocks as $block) {
                        $flexibleContent[] = [
                            'acf_fc_layout' => 'content_block',
                            'content' => $block['content'],
                            'image' => $this->mappings['images'][$block['image_id']] ?? null,
                            'alignment' => $block['alignment'],
                            'link_text' => $block['link_text'],
                            'link' => $block['link'],
                            'youtube_video_id' => $block['youtube_video_id']
                        ];
                    }

                    update_field('content_blocks', $flexibleContent, $postId);
                }
            }
        }

        $this->logProgress("Migrated content blocks");
    }

    private function setupRelationships() {
        $this->logProgress("Setting up relationships...");

        // Accommodation to Destinations relationships
        $stmt = $this->sourceDB->query("
            SELECT accommodation_id, destination_id, featured, `order`
            FROM accommodation_destinations
        ");

        $relationships = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($relationships as $rel) {
            $accommodationPost = $this->mappings['accommodation'][$rel['accommodation_id']] ?? null;
            $destinationPost = $this->mappings['destinations'][$rel['destination_id']] ?? null;

            if ($accommodationPost && $destinationPost) {
                // Use ACF Relationship field
                $currentDestinations = get_field('destinations', $accommodationPost) ?: [];
                $currentDestinations[] = $destinationPost;
                update_field('destinations', $currentDestinations, $accommodationPost);

                // Store featured/order metadata
                if ($rel['featured']) {
                    add_post_meta($accommodationPost, 'featured_destination_' . $destinationPost, true);
                }
                add_post_meta($accommodationPost, 'destination_order_' . $destinationPost, $rel['order']);
            }
        }

        $this->logProgress("Relationships configured");
    }
}
```

#### **Gravity Forms Integration for Travel Plans**
```php
// Travel Plans migration to Gravity Forms
function migrateTravelPlansToGravityForms() {

    // Create Gravity Form programmatically
    $form = [
        'title' => 'Travel Plans',
        'description' => 'Plan your perfect trip to the USA or Canada',
        'fields' => [
            [
                'type' => 'select',
                'id' => 1,
                'label' => 'Title',
                'choices' => [
                    ['text' => 'Mr', 'value' => 'Mr'],
                    ['text' => 'Mrs', 'value' => 'Mrs'],
                    ['text' => 'Miss', 'value' => 'Miss'],
                    ['text' => 'Ms', 'value' => 'Ms']
                ],
                'isRequired' => true
            ],
            [
                'type' => 'text',
                'id' => 2,
                'label' => 'First Name',
                'isRequired' => true
            ],
            [
                'type' => 'text',
                'id' => 3,
                'label' => 'Last Name',
                'isRequired' => true
            ],
            [
                'type' => 'email',
                'id' => 4,
                'label' => 'Email Address',
                'isRequired' => true
            ],
            [
                'type' => 'phone',
                'id' => 5,
                'label' => 'Telephone Number',
                'isRequired' => true
            ],
            [
                'type' => 'select',
                'id' => 6,
                'label' => 'Destination Country',
                'choices' => [
                    ['text' => 'USA', 'value' => 'USA'],
                    ['text' => 'Canada', 'value' => 'Canada']
                ],
                'isRequired' => true
            ]
        ]
    ];

    $form_id = GFAPI::add_form($form);

    // Migrate existing travel plan submissions
    $stmt = $sourceDB->query("SELECT * FROM travel_plans");
    $travelPlans = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($travelPlans as $plan) {
        $entry = [
            'form_id' => $form_id,
            'date_created' => $plan['created'],
            'is_starred' => 0,
            'is_read' => 1,
            'ip' => '127.0.0.1',
            'source_url' => 'https://www.bon-voyage.co.uk/travel_plans/add',
            'user_agent' => 'Migration Script',
            '1' => $plan['title'],
            '2' => $plan['first_name'],
            '3' => $plan['last_name'],
            '4' => $plan['email_address'],
            '5' => $plan['telephone_number'],
            '6' => $plan['destination_country']
        ];

        GFAPI::add_entry($entry);
    }
}
```

### **Phase 4: Integration Testing and Third-Party Services (Days 11-13)**

#### **Third-Party Integrations**
```php
// Feefo Reviews Integration
class FeefoIntegration {

    private $apiKey;
    private $merchantId;

    public function __construct() {
        $this->apiKey = get_option('feefo_api_key');
        $this->merchantId = get_option('feefo_merchant_id');
    }

    public function getReviews($limit = 10) {
        $url = "https://api.feefo.com/api/10/reviews/merchant/{$this->merchantId}";
        $response = wp_remote_get($url . "?limit={$limit}", [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->apiKey
            ]
        ]);

        if (is_wp_error($response)) {
            return [];
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        return $data['reviews'] ?? [];
    }

    public function displayReviews($atts) {
        $atts = shortcode_atts([
            'limit' => 5,
            'template' => 'default'
        ], $atts);

        $reviews = $this->getReviews($atts['limit']);

        ob_start();
        foreach ($reviews as $review) {
            echo '<div class="feefo-review">';
            echo '<div class="feefo-review__rating">' . str_repeat('★', $review['rating']) . '</div>';
            echo '<div class="feefo-review__text">' . esc_html($review['description']) . '</div>';
            echo '<div class="feefo-review__author">' . esc_html($review['customer']['display_name']) . '</div>';
            echo '</div>';
        }
        return ob_get_clean();
    }
}

// Register Feefo shortcode
add_shortcode('feefo_reviews', [new FeefoIntegration(), 'displayReviews']);
```

#### **Enhanced Search Functionality**
```php
// Enhanced WordPress search for destinations and accommodation
function bonvoyage_custom_search($query) {
    if (!is_admin() && $query->is_main_query() && $query->is_search()) {
        $query->set('post_type', ['post', 'destination', 'accommodation', 'activity']);

        // Add meta query for location-based search
        if (isset($_GET['location'])) {
            $meta_query = [
                'relation' => 'OR',
                [
                    'key' => 'location',
                    'value' => sanitize_text_field($_GET['location']),
                    'compare' => 'LIKE'
                ],
                [
                    'key' => 'destinations',
                    'value' => sanitize_text_field($_GET['location']),
                    'compare' => 'LIKE'
                ]
            ];
            $query->set('meta_query', $meta_query);
        }
    }
}
add_action('pre_get_posts', 'bonvoyage_custom_search');

// AJAX search functionality
function bonvoyage_ajax_search() {
    check_ajax_referer('bonvoyage_nonce', 'nonce');

    $search_term = sanitize_text_field($_POST['search_term']);
    $post_type = sanitize_text_field($_POST['post_type']) ?: 'any';

    $args = [
        's' => $search_term,
        'post_type' => $post_type,
        'posts_per_page' => 10,
        'post_status' => 'publish'
    ];

    $query = new WP_Query($args);
    $results = [];

    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            $results[] = [
                'id' => get_the_ID(),
                'title' => get_the_title(),
                'url' => get_permalink(),
                'excerpt' => get_the_excerpt(),
                'thumbnail' => get_the_post_thumbnail_url(get_the_ID(), 'thumbnail')
            ];
        }
    }

    wp_reset_postdata();
    wp_send_json_success($results);
}
add_action('wp_ajax_bonvoyage_search', 'bonvoyage_ajax_search');
add_action('wp_ajax_nopriv_bonvoyage_search', 'bonvoyage_ajax_search');
```

<div style="page-break-before: always;"></div>

## Database Migration Analysis

### **Current Database Schema Overview**

The CakePHP application uses a complex database schema with **30+ core tables** and extensive relationships:

#### **Core Content Tables:**
- `destinations` (589 records) - Hierarchical tree structure with lft/rght
- `accommodation` (671 records) - Hotels/lodging with complex relationships
- `activities` (719 records) - Things to do at destinations
- `holiday_types` (23 records) - Travel categories/themes
- `itineraries` (197 records) - Multi-day travel plans
- `pages` (60 records) - CMS pages with tree structure
- `spotlights` (250 records) - Featured content/offers

#### **Relationship Tables (Many-to-Many):**
- `accommodation_destinations` (3,087 records)
- `activities_destinations` (3,791 records)
- `destinations_itineraries` (5,625 records)
- `holiday_types_on_destinations` (671 records)
- `accommodation_holiday_types` (741 records)

#### **Media & Content Management:**
- `images` (7,887 records) - File management system
- `image_versions` (8 records) - Different image sizes/crops
- `content_blocks` (4,986 records) - Flexible content blocks
- `custom_image_versions` - Custom image cropping data

#### **User & Form Data:**
- `users` (27 records) - Admin users with ACL
- `travel_plans` (18 records) - Lead generation forms
- `quote_requests` (8,981 records) - Legacy quote forms
- `contacts` (3,294 records) - Contact form submissions

#### **Complex Features:**
- **ACL System**: `acos`, `aros`, `aros_acos` tables for permissions
- **Tree Structures**: Nested set model (lft/rght) for hierarchical data
- **Image Management**: Custom versioning and cropping system
- **Content Blocks**: Polymorphic content system

### **WordPress + ACF Migration Script Feasibility**

#### ✅ **HIGHLY FEASIBLE** - Automated Conversion Possible

**Migration Strategy:**
```php
<?php
// WordPress Migration Script Structure

class BonVoyageMigrationScript {

    public function migrate() {
        $this->setupWordPressStructure();
        $this->migrateUsers();
        $this->migrateImages();
        $this->migrateDestinations();
        $this->migrateAccommodation();
        $this->migrateActivities();
        $this->migrateHolidayTypes();
        $this->migrateItineraries();
        $this->migratePages();
        $this->migrateSpotlights();
        $this->migrateTravelPlans();
        $this->setupRelationships();
        $this->migrateContentBlocks();
    }

    private function setupWordPressStructure() {
        // Register Custom Post Types
        $this->registerPostType('destination', [
            'hierarchical' => true,
            'supports' => ['title', 'editor', 'thumbnail', 'page-attributes']
        ]);

        $this->registerPostType('accommodation');
        $this->registerPostType('activity');
        $this->registerPostType('holiday_type');
        $this->registerPostType('itinerary');

        // Register ACF Field Groups
        $this->setupACFFields();
    }
}
```

#### **Migration Complexity Assessment**

| Component | Complexity | Automation Level | Estimated Time |
|-----------|------------|------------------|----------------|
| **Users & Permissions** | ✅ Low | 95% Automated | 2 hours |
| **Images & Media** | ✅ Medium | 90% Automated | 1 day |
| **Destinations (Hierarchical)** | ⚠️ Medium | 85% Automated | 2 days |
| **Accommodation** | ✅ Medium | 90% Automated | 1 day |
| **Activities & Holiday Types** | ✅ Low | 95% Automated | 4 hours |
| **Itineraries** | ⚠️ Medium | 80% Automated | 1 day |
| **Content Blocks** | ⚠️ Complex | 75% Automated | 2 days |
| **Many-to-Many Relationships** | ⚠️ Complex | 70% Automated | 2 days |
| **Travel Plans → Gravity Forms** | ✅ Medium | 85% Automated | 1 day |
| **URL Redirects & SEO** | ✅ Medium | 90% Automated | 4 hours |

**Total Migration Effort: 10-12 days**

#### **Success Factors:**
- ✅ **Clean Data Structure**: Well-normalized database
- ✅ **Standard Patterns**: Common CMS patterns (hierarchies, relationships)
- ✅ **WordPress Flexibility**: ACF handles complex field structures
- ✅ **Existing Tools**: WP-CLI, ACF Pro, Gravity Forms APIs

#### **Risk Mitigation:**
- **Staging Environment**: Test migration multiple times
- **Data Validation**: Compare record counts and relationships
- **Rollback Plan**: Database backups at each step
- **Manual QA**: Review critical content and functionality

### **Recommendation: Proceed with Automated Migration**

The database structure is **highly suitable for automated migration** to WordPress + ACF. The well-organized schema, standard relationships, and WordPress's flexible content system make this a **low-risk, high-success** migration path.

<div style="page-break-before: always;"></div>

## CakePHP Migration Feasibility Analysis

### **Current Application Audit**

#### **Core Functionality Identified:**
1. **Travel Planning System**
   - Complex multi-step travel plan forms with validation
   - Integration with external CRM system (AddQuoteRequest_UTM API)
   - ReCaptcha v3 integration for spam protection
   - Email newsletter subscription system

2. **Content Management**
   - Hierarchical destination management (USA/Canada with regions)
   - Holiday types and itineraries
   - Image management with multiple versions/crops
   - Page management with navigation trees
   - Spotlight/featured content system

3. **User Authentication & Authorization**
   - ACL-based permission system
   - Admin interface (/webadmin) with role-based access
   - Session management and history tracking

4. **Third-Party Integrations**
   - **Feefo Reviews**: XML API integration for customer reviews
   - **Google APIs**: Custom search and ReCaptcha
   - **Email Services**: Custom email list subscription service
   - **CRM Integration**: SOAP/REST API for lead management

5. **Custom Components & Features**
   - Image processing and versioning system
   - Navigation component with mega menu support
   - Filter component for search/listing pages
   - History component for breadcrumb navigation
   - Custom caching system

#### **Database Schema Complexity:**
- **50+ tables** including destinations, accommodation, itineraries, pages, users
- **Complex relationships**: Tree structures, HABTM associations
- **Custom fields**: Meta data, SEO fields, publishing workflow
- **File attachments**: Image associations with versioning

### **Migration Path Analysis**

#### **Option 1: CakePHP 1.2.12 → PHP 7.4/8.1 (Direct Migration)**

**Feasibility: ❌ NOT RECOMMENDED**

**Critical Blockers:**
```php
// CakePHP 1.2.12 uses deprecated PHP features
var $name = 'Model';           // PHP 7.4+ deprecates var keyword
var $components = array();     // Old array syntax
mysql_* functions              // Removed in PHP 7.0
split() function              // Removed in PHP 7.0
ereg_* functions              // Removed in PHP 7.0
```

**Major Compatibility Issues:**
1. **PHP Language Changes**
   - `var` keyword deprecated → use `public/private/protected`
   - `mysql_*` functions removed → must use `mysqli_*` or PDO
   - `split()` function removed → use `explode()` or `preg_split()`
   - Constructor method changes (`__construct()` vs class name)

2. **CakePHP 1.2.12 Framework Issues**
   - No official PHP 7+ support
   - Core framework uses deprecated functions
   - Security vulnerabilities (no updates since 2012)
   - Incompatible with modern PHP error handling

**Estimated Effort: 6-12 months**
- Rewrite 80% of framework core files
- Update all models, controllers, and views
- Extensive testing and debugging
- High risk of introducing bugs

#### **Option 2: Rebuild in WordPress + Gravity Forms + ACF**

**Feasibility: ✅ HIGHLY RECOMMENDED**

**Advantages:**
- **Content Management**: WordPress excels at content management
- **Form Handling**: Gravity Forms handles complex multi-step forms
- **Custom Fields**: ACF provides flexible field management
- **Existing Integration**: Blog already uses WordPress
- **Maintenance**: Easier to find WordPress developers

**Implementation Strategy:**
```php
// Travel Plan Form (Gravity Forms)
add_action('gform_after_submission_1', 'handle_travel_plan_submission', 10, 2);

function handle_travel_plan_submission($entry, $form) {
    // Integrate with existing CRM API
    $crm_data = prepare_crm_data($entry);
    send_to_crm($crm_data);

    // Newsletter subscription
    if ($entry['email_consent']) {
        subscribe_to_newsletter($entry['email'], $entry['first_name'], $entry['last_name']);
    }
}

// Custom Post Types for Destinations
register_post_type('destination', [
    'public' => true,
    'hierarchical' => true,
    'supports' => ['title', 'editor', 'thumbnail', 'custom-fields']
]);

// ACF Fields for destination data
acf_add_local_field_group([
    'key' => 'destination_fields',
    'title' => 'Destination Information',
    'fields' => [
        ['key' => 'map_latitude', 'name' => 'map_latitude', 'type' => 'number'],
        ['key' => 'map_longitude', 'name' => 'map_longitude', 'type' => 'number'],
        ['key' => 'from_price', 'name' => 'from_price', 'type' => 'number']
    ]
]);
```

**Migration Mapping:**
- **Destinations** → Custom Post Type with ACF fields
- **Travel Plans** → Gravity Forms with custom submission handling
- **User Management** → WordPress users with custom roles
- **Image Management** → WordPress media library with custom sizes
- **Navigation** → WordPress menus with custom walker

**Estimated Effort: 3-4 months**
- Custom post types and fields: 2 weeks
- Form migration to Gravity Forms: 3 weeks
- Template development: 4 weeks
- API integrations: 2 weeks
- Testing and refinement: 4 weeks

### **Recommendation Matrix**

| Criteria | CakePHP Migration | WordPress + GF + ACF |
|----------|-------------------|---------------------|
| **Development Time** | 6-12 months | 3-4 months |
| **Technical Risk** | ❌ Very High | ✅ Low |
| **Maintenance Cost** | ❌ High | ✅ Low |
| **Developer Availability** | ❌ Very Limited | ✅ High |
| **Future Scalability** | ❌ Poor | ✅ Excellent |
| **Security Updates** | ❌ None | ✅ Regular |
| **Performance** | ❌ Poor | ✅ Excellent |

### **Final Recommendation**

**Primary Choice: WordPress + Gravity Forms + ACF**
- Fastest implementation (3-4 months)
- Lowest risk and cost
- Leverages existing WordPress blog
- Easy to maintain and extend
- Large developer pool available

**Avoid: Direct CakePHP Migration**
- Extremely high risk and cost
- No security updates available
- Limited developer expertise
- Poor return on investment

<div style="page-break-before: always;"></div>

## Blog Migration: Bedrock/Timber to Vanilla WordPress

### **Current Blog Architecture Analysis**

The existing blog uses a **modern but complex** setup:

#### **Current Stack:**
- **Bedrock**: WordPress boilerplate with Composer dependency management
- **Timber**: Twig templating engine for WordPress
- **Lumberjack**: Object-oriented framework built on Timber
- **Custom Structure**: Non-standard directory layout

#### **Current Blog Structure:**
```
app/webroot/blog/
├── wp/                    # WordPress core (via Composer)
├── app/
│   ├── mu-plugins/        # Must-use plugins (Timber)
│   ├── plugins/           # Standard plugins
│   ├── themes/bonvoyage/  # Custom theme
│   │   ├── views/         # Twig templates
│   │   ├── lumberjack/    # Framework files
│   │   └── assets/        # CSS/JS assets
│   └── uploads/           # Media files
└── wp-config.php          # WordPress configuration
```

#### **Template Structure (Twig):**
- `base.twig` - Main layout template
- `posts.twig` - Blog listing page
- `single.twig` - Individual post template
- `components/` - Reusable template components

#### **Current Functionality:**
- ✅ **Simple Blog**: Standard WordPress posts and pages
- ✅ **Custom Styling**: Matches main site design
- ✅ **Navigation Integration**: Shared header/footer with main site
- ✅ **Search Functionality**: Custom search toggle implementation
- ✅ **Social Sharing**: Twitter/Facebook integration
- ✅ **Feefo Reviews**: Customer review integration

### **Migration to Vanilla WordPress**

#### **Feasibility: ✅ HIGHLY FEASIBLE** - Simple blog structure
#### **Effort: 4-5 days** for complete migration

#### **Template Conversion (Twig → PHP)**

**Current Twig Template:**
```twig
{% extends "base.twig" %}
{% block content %}
    {% for post in posts %}
        <div class="entry">
            <h1 class="entry__hd">
                <a href="{{ post.permalink }}">{{ post.title }}</a>
            </h1>
            {{ post.get_preview(50, false, 'Read More...', true) }}
        </div>
    {% endfor %}
{% endblock %}
```

**Converted PHP Template:**
```php
<?php get_header(); ?>
<div class="page-content-body__content page-content-body__content--blog">
    <div class="section-content-wrapper">
        <div class="section-content section-content--blog">
            <div class="section-content__inner section-content__inner--blog">
                <?php if (have_posts()) : ?>
                    <?php while (have_posts()) : the_post(); ?>
                        <div class="entry">
                            <div class="entry__content">
                                <h1 class="entry__hd">
                                    <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                </h1>
                                <?php if (has_post_thumbnail()) : ?>
                                    <div class="entry__thumbnail">
                                        <?php the_post_thumbnail('medium'); ?>
                                    </div>
                                <?php endif; ?>
                                <ul class="entry__meta entry__meta--date-author">
                                    <li>Published on <a href="<?php the_permalink(); ?>"><?php the_date(); ?></a></li>
                                    <li>Author <a href="<?php echo get_author_posts_url(get_the_author_meta('ID')); ?>"><?php the_author(); ?></a></li>
                                </ul>
                                <p><?php echo wp_trim_words(get_the_content(), 50, '... <a href="' . get_permalink() . '">Read More</a>'); ?></p>
                            </div>
                        </div>
                    <?php endwhile; ?>

                    <?php the_posts_pagination(array(
                        'prev_text' => '&laquo; Previous',
                        'next_text' => 'Next &raquo;',
                    )); ?>
                <?php else : ?>
                    <p>No results found.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php get_footer(); ?>
```

#### **Directory Structure Migration**

**Target Vanilla WordPress Structure:**
```
wp-content/
├── themes/bonvoyage/
│   ├── index.php          # Main template
│   ├── header.php         # Header template
│   ├── footer.php         # Footer template
│   ├── single.php         # Single post template
│   ├── archive.php        # Archive template
│   ├── search.php         # Search results
│   ├── 404.php           # Error page
│   ├── functions.php      # Theme functions
│   ├── style.css         # Main stylesheet
│   └── assets/           # CSS/JS/Images
├── plugins/              # Standard plugins
└── uploads/              # Media files
```

#### **Migration Script**

```php
<?php
// blog-migration.php - Bedrock to Vanilla WordPress Migration

class BlogMigrationScript {

    public function migrate() {
        $this->setupVanillaStructure();
        $this->convertTemplates();
        $this->migrateFunctionality();
        $this->updateConfiguration();
        $this->testMigration();
    }

    private function setupVanillaStructure() {
        // Create standard WordPress directory structure
        $this->createDirectories([
            'wp-content/themes/bonvoyage',
            'wp-content/themes/bonvoyage/assets/css',
            'wp-content/themes/bonvoyage/assets/js',
            'wp-content/themes/bonvoyage/assets/img'
        ]);
    }

    private function convertTemplates() {
        $templates = [
            'base.twig' => 'header.php + footer.php',
            'posts.twig' => 'index.php',
            'single.twig' => 'single.php',
            'archive.twig' => 'archive.php',
            'search.twig' => 'search.php',
            '404.twig' => '404.php'
        ];

        foreach ($templates as $twig => $php) {
            $this->convertTemplate($twig, $php);
        }
    }

    private function migrateFunctionality() {
        // Convert Timber/Lumberjack functionality to standard WordPress
        $this->migrateSearchToggle();
        $this->migrateSocialSharing();
        $this->migrateFeefoIntegration();
        $this->migrateNavigationIntegration();
    }
}
```

#### **Key Functionality Migration**

**Search Toggle (Already Implemented):**
```php
// Current implementation in functions.php is already vanilla WordPress
function bonvoyage_enqueue_assets() {
    wp_enqueue_script('bonvoyage-search-toggle',
        get_template_directory_uri() . '/assets/js/search-toggle.js');
}
add_action('wp_enqueue_scripts', 'bonvoyage_enqueue_assets');
```

**Navigation Integration:**
```php
// functions.php - Integrate with main site navigation
function bonvoyage_shared_navigation() {
    // Load navigation from main site endpoints
    $megamenu = wp_remote_get(home_url('/megamenu'));
    $mmenu = wp_remote_get(home_url('/mmenu'));

    if (!is_wp_error($megamenu)) {
        set_transient('bonvoyage_megamenu', wp_remote_retrieve_body($megamenu), HOUR_IN_SECONDS);
    }

    if (!is_wp_error($mmenu)) {
        set_transient('bonvoyage_mmenu', wp_remote_retrieve_body($mmenu), HOUR_IN_SECONDS);
    }
}
add_action('init', 'bonvoyage_shared_navigation');
```

### **Migration Timeline & Effort**

| Task | Complexity | Time | Dependencies |
|------|------------|------|--------------|
| **Template Conversion** | ✅ Medium | 2 days | Design decision |
| **Functionality Migration** | ✅ Low | 1 day | Template completion |
| **Testing & QA** | ✅ Low | 1 day | All components |
| **Total (Direct Migration)** | | **4-5 days** | |

### **Benefits of Vanilla WordPress Migration**
- ✅ **Simpler Deployment**: Standard WordPress hosting
- ✅ **Easier Maintenance**: No Composer dependencies
- ✅ **Better Compatibility**: Standard plugin ecosystem
- ✅ **Developer Availability**: Larger talent pool
- ✅ **Lower Risk**: Proven, stable platform

The blog migration is **highly feasible** and can be completed in **4-5 days** for a direct conversion, maintaining the current design and functionality while moving to a standard WordPress structure.

<div style="page-break-before: always;"></div>

## Project Management Framework

### **Dual Workstream Coordination**

#### **Communication Strategy**
- **Daily Standups**: 15-minute alignment meetings
- **Weekly Progress Reviews**: 1-hour detailed status updates
- **Integration Checkpoints**: Real-time collaboration during critical phases
- **Documentation Sharing**: Centralized knowledge base

#### **Collaboration Timeline**

```mermaid
gantt
    title Project Collaboration Points
    dateFormat X
    axisFormat Day %d

    section Critical Collaboration
    Joint Planning Sessions      :collab1, 1, 2d
    Database Integration        :collab2, 6, 2d
    Integration Testing         :collab3, 11, 3d
    Deployment Coordination     :collab4, 14, 3d

    section AWS Workstream
    Infrastructure Work         :aws-work, 1, 10d
    Support & Monitoring        :aws-support, 11, 6d

    section WordPress Workstream
    Development Work           :wp-work, 1, 16d
```

#### **Risk Management**

**Technical Risk Mitigation:**
- **Database Migration**: Comprehensive backup and rollback procedures
- **Infrastructure Changes**: Staging environment for testing
- **Application Bugs**: Parallel development and testing environments
- **Performance Issues**: Load testing and optimization phases

**Project Risk Mitigation:**
- **Communication Gaps**: Daily standups and shared documentation
- **Dependency Delays**: Buffer time built into critical path items
- **Integration Issues**: Joint testing phases and real-time collaboration
- **Knowledge Silos**: Cross-training and documentation sharing

#### **Quality Assurance Process**

**Testing Strategy:**
- **Automated Testing**: Database migration validation scripts
- **Manual Testing**: Critical user journeys and forms
- **Performance Testing**: Basic load testing with free tools
- **Security Testing**: Basic vulnerability scanning

**Acceptance Criteria:**
- All existing content migrated successfully
- Contact forms and lead generation working
- Site performance equal or better than current
- Mobile responsiveness maintained
- SEO URLs and redirects functioning

<div style="page-break-before: always;"></div>

## Testing and Quality Assurance

### **Comprehensive Testing Checklist**

```bash
#!/bin/bash
# comprehensive-testing.sh

echo "=== Bon Voyage WordPress Testing Checklist ==="

echo "□ Content Migration Validation"
echo "  □ All destinations migrated (expected: 589)"
echo "  □ All accommodation migrated (expected: 671)"
echo "  □ All activities migrated (expected: 719)"
echo "  □ All images transferred (expected: 7,887)"
echo "  □ Content blocks converted to ACF"

echo "□ Functionality Testing"
echo "  □ Travel plans form submission"
echo "  □ Contact form functionality"
echo "  □ Search functionality"
echo "  □ Navigation menus"
echo "  □ Mobile responsiveness"

echo "□ Third-Party Integrations"
echo "  □ Feefo reviews display"
echo "  □ Google Analytics tracking"
echo "  □ ReCaptcha protection"
echo "  □ CRM lead submission"

echo "□ Performance Testing"
echo "  □ Page load speed < 3 seconds"
echo "  □ Database query optimization"
echo "  □ Image optimization"
echo "  □ CDN functionality"

echo "□ SEO Validation"
echo "  □ URL redirects working"
echo "  □ Meta descriptions preserved"
echo "  □ Structured data markup"
echo "  □ XML sitemap generation"

echo "□ Security Testing"
echo "  □ User permissions correct"
echo "  □ Form validation working"
echo "  □ File upload restrictions"
echo "  □ SSL certificate valid"
```

### **Performance Optimization**

```php
// Caching and performance optimizations
function bonvoyage_performance_optimizations() {

    // Enable object caching for expensive queries
    function get_cached_destinations($parent_id = 0) {
        $cache_key = 'destinations_' . $parent_id;
        $destinations = wp_cache_get($cache_key, 'bonvoyage');

        if (false === $destinations) {
            $destinations = get_posts([
                'post_type' => 'destination',
                'post_parent' => $parent_id,
                'posts_per_page' => -1,
                'orderby' => 'menu_order',
                'order' => 'ASC'
            ]);

            wp_cache_set($cache_key, $destinations, 'bonvoyage', HOUR_IN_SECONDS);
        }

        return $destinations;
    }

    // Lazy load images
    function add_lazy_loading() {
        add_filter('wp_get_attachment_image_attributes', function($attr) {
            $attr['loading'] = 'lazy';
            return $attr;
        });
    }
    add_action('init', 'add_lazy_loading');
}
add_action('init', 'bonvoyage_performance_optimizations');
```

### **SEO Migration and Preservation**

```php
// SEO preservation and enhancement
function bonvoyage_seo_migration() {

    // Preserve meta descriptions from CakePHP
    function migrate_meta_descriptions() {
        $posts = get_posts([
            'post_type' => ['destination', 'accommodation', 'activity'],
            'posts_per_page' => -1
        ]);

        foreach ($posts as $post) {
            $meta_description = get_field('meta_description', $post->ID);
            if ($meta_description) {
                update_post_meta($post->ID, '_yoast_wpseo_metadesc', $meta_description);
            }
        }
    }

    // Set up URL redirects from old CakePHP URLs
    function setup_url_redirects() {
        $redirects = [
            '/destinations/view/(.+)' => '/destinations/$1/',
            '/accommodation/view/(.+)' => '/accommodation/$1/',
            '/activities/view/(.+)' => '/activities/$1/',
            '/travel_plans/add' => '/travel-plans/',
            '/pages/view/(.+)' => '/$1/'
        ];

        foreach ($redirects as $old_pattern => $new_url) {
            add_rewrite_rule($old_pattern, $new_url, 'top');
        }

        flush_rewrite_rules();
    }
}
add_action('init', 'bonvoyage_seo_migration');
```

---

**This technical summary provides comprehensive implementation details for both workstreams, ensuring successful migration execution with minimal risk and maximum efficiency.**
```
```
```
