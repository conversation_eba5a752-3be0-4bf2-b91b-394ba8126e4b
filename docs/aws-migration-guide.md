# AWS Migration Guide for Bon Voyage CakePHP + WordPress

This guide outlines the simplest approaches to migrate the Bon Voyage CakePHP application and WordPress blog to AWS while maintaining PHP 7.2 compatibility and scalability.

## Current Architecture Overview

- **CakePHP Application**: PHP 7.2 with Apache, document root at `app/webroot`
- **WordPress Blog**: Integrated at `/blog` path, using Timber framework
- **Database**: MariaDB 10.11 (MySQL compatible)
- **Assets**: File uploads and resources stored locally
- **Current Deployment**: Elastic Beanstalk (existing `.ebextensions` configs found)

## PHP 7.2 Compatibility Analysis

### AWS Platform Support Status
**⚠️ CRITICAL**: PHP 7.2 reached End of Life (EOL) on November 30, 2020

#### Current AWS Support:
- **Elastic Beanstalk**: PHP 7.2 platform deprecated (last supported version: PHP 7.2.34)
- **Amazon Linux 2**: PHP 7.2 available via `amazon-linux-extras` but deprecated
- **Amazon Linux 2023**: PHP 7.2 NOT available (minimum PHP 8.0)
- **Docker Images**: `php:7.2-apache` still available but no security updates

#### Recommended Approaches:

**Option A: Custom Docker with PHP 7.2 (Short-term)**
```dockerfile
# Use official PHP 7.2 image (frozen, no updates)
FROM php:7.2.34-apache

# Install security patches at OS level only
RUN apt-get update && apt-get upgrade -y \
    && apt-get install -y security-updates
```
- **Pros**: Maintains exact PHP 7.2 compatibility
- **Cons**: No PHP security updates, compliance risks
- **Timeline**: 3-6 months maximum before forced upgrade

**Option B: PHP 7.4 Migration (Recommended)**
```dockerfile
# Upgrade to PHP 7.4 (supported until Nov 2022, but more secure)
FROM php:7.4-apache
```
- **Pros**: Better security, AWS platform support
- **Cons**: Requires code compatibility testing
- **Timeline**: 2-4 weeks for testing and migration

**Option C: PHP 8.0+ Migration (Future-proof)**
```dockerfile
# Upgrade to PHP 8.1 (LTS until Nov 2024)
FROM php:8.1-apache
```
- **Pros**: Long-term support, performance improvements
- **Cons**: Significant code changes required
- **Timeline**: 4-8 weeks for full migration

#### PHP 7.2 Compatibility Testing Required:
```bash
# Test current codebase compatibility
composer require --dev phpcompatibility/php-compatibility
vendor/bin/phpcs --standard=PHPCompatibility --runtime-set testVersion 7.4 app/

# WordPress compatibility check
wp core check-update --allow-root
wp plugin list --update=available --format=table
```

### Docker Image Recommendations:

**For PHP 7.2 (Temporary Solution):**
```dockerfile
FROM php:7.2.34-apache
# Pin to specific version to avoid breaking changes
# Note: No security updates available
```

**For PHP 7.4 (Recommended Migration):**
```dockerfile
FROM php:7.4.33-apache
# Last version with security support
# Compatible with most PHP 7.2 code
```

### AWS Instance Compatibility:
- **EC2 Instances**: All current instance types support custom Docker images
- **Elastic Beanstalk**: Requires Docker platform (not PHP platform)
- **App Runner**: Full Docker support with any PHP version
- **ECS/Fargate**: Complete flexibility with Docker images

## Migration Options

### Option 1: AWS Elastic Beanstalk (Recommended - Existing Setup)

**Pros**: Already configured, familiar deployment process, auto-scaling, load balancing
**Cons**: Less control over infrastructure, potential vendor lock-in

#### Current Beanstalk Configuration
- Platform: PHP 7.2 on Amazon Linux
- Region: eu-west-2 (London)
- Environment: Bvwww-env-1
- EFS integration for shared file storage
- Automated deployments via GitLab CI

#### Migration Steps:
1. **Update Environment Variables**
   ```bash
   # Set production environment variables
   eb setenv WP_ENV=production \
            RDS_NAME=your-rds-database \
            RDS_USERNAME=your-db-user \
            RDS_PASSWORD=your-db-password \
            RDS_HOSTNAME=your-rds-endpoint \
            WP_HOME=https://www.bon-voyage.co.uk/blog \
            FORCE_SSL=true
   ```

2. **Database Migration**
   - Create RDS MySQL 5.7/8.0 instance
   - Export current database: `mysqldump bon-voyage > backup.sql`
   - Import to RDS: `mysql -h rds-endpoint -u username -p database_name < backup.sql`

3. **WordPress Media Storage Setup (S3 + EBS)**
   - S3 bucket for WordPress images: `s3://bon-voyage-wp-media`
   - EBS volume mounted to `/var/www/html/app/webroot/blog/app/uploads`
   - S3FS for transparent S3 mounting to EBS
   - Sync existing uploads: `aws s3 sync /local/uploads s3://bon-voyage-wp-media/uploads`

4. **Deploy**
   ```bash
   eb deploy Bvwww-env-1
   ```

### Option 2: AWS App Runner with Docker

**Pros**: Fully managed containers, automatic scaling, simpler than ECS
**Cons**: Newer service, less ecosystem integration

#### Setup Steps:
1. **Create Dockerfile** (based on existing `.docker/php/Dockerfile`)
2. **Configure App Runner**
   - Source: ECR or GitHub
   - Runtime: Docker
   - Auto-scaling: 1-10 instances
3. **Database**: RDS MySQL
4. **File Storage**: EFS or S3

### Option 3: Cloudways Managed Hosting

**Pros**: Managed PHP hosting, easy setup, built-in optimizations
**Cons**: Less AWS integration, potential performance limitations

#### Cloudways Setup:
1. **Create Server**
   - Provider: AWS
   - Server Size: 2GB+ RAM
   - PHP Version: 7.2
   - Location: London (eu-west-2)

2. **Application Setup**
   - Upload codebase via SFTP/Git
   - Configure database connection
   - Set up SSL certificate

## Recommended Migration Plan

### Phase 1: Infrastructure Setup (Day 1-2)
1. **RDS Database**
   ```bash
   # Create RDS MySQL instance
   aws rds create-db-instance \
     --db-instance-identifier bon-voyage-prod \
     --db-instance-class db.t3.micro \
     --engine mysql \
     --engine-version 8.0 \
     --allocated-storage 20 \
     --master-username admin \
     --master-user-password [secure-password] \
     --vpc-security-group-ids sg-xxxxxxxxx \
     --db-subnet-group-name default
   ```

2. **S3 Bucket for Assets**
   ```bash
   aws s3 mb s3://bon-voyage-assets-prod
   aws s3api put-bucket-policy --bucket bon-voyage-assets-prod --policy file://s3-policy.json
   ```

3. **CloudFront Distribution**
   - Origin: S3 bucket for assets
   - Alternate origin: Elastic Beanstalk environment
   - SSL Certificate: ACM certificate

### Phase 2: Application Configuration (Day 2-3)
1. **Update Environment Configuration**
   - Modify `app/config/.env` for production
   - Update WordPress URLs in `app/config/application.php`
   - Configure SSL redirects

2. **Database Migration**
   - Export development database
   - Import to RDS with production URLs
   - Update WordPress site URLs

3. **WordPress Media Storage Configuration (S3 + EBS)**
   - Create S3 bucket: `s3://bon-voyage-wp-media`
   - Install and configure S3FS on EC2 instances
   - Mount S3 bucket to EBS volume at `/var/www/html/app/webroot/blog/app/uploads`
   - Configure WordPress to use mounted storage path
   - Set up automatic sync and local caching for performance

### Phase 3: Deployment & Testing (Day 3-4)
1. **Deploy to Staging Environment**
   ```bash
   eb create staging-environment --platform "PHP 7.2"
   eb deploy staging-environment
   ```

2. **Testing Checklist**
   - [ ] CakePHP application loads correctly
   - [ ] WordPress blog accessible at `/blog`
   - [ ] Database connections working
   - [ ] File uploads functional
   - [ ] SSL certificates valid
   - [ ] Navigation between main site and blog works

3. **Production Deployment**
   ```bash
   eb deploy Bvwww-env-1
   ```

### Phase 4: DNS & CDN Setup (Day 4-5)
1. **CloudFlare Configuration**
   - Update A records to point to Elastic Beanstalk
   - Configure SSL/TLS settings
   - Set up page rules for caching
   - Enable security features

2. **Performance Optimization**
   - Configure CloudFront for static assets
   - Enable Gzip compression
   - Set up database connection pooling

## Environment Variables Required

```bash
# Database
RDS_NAME=bon_voyage_prod
RDS_USERNAME=admin
RDS_PASSWORD=secure_password_here
RDS_HOSTNAME=bon-voyage-prod.xxxxxxxxx.eu-west-2.rds.amazonaws.com

# WordPress
WP_ENV=production
WP_HOME=https://www.bon-voyage.co.uk/blog
WP_SITEURL=https://www.bon-voyage.co.uk/blog/wp
FORCE_SSL=true

# Security Keys (generate at https://api.wordpress.org/secret-key/1.1/salt/)
AUTH_KEY=generate_unique_key
SECURE_AUTH_KEY=generate_unique_key
LOGGED_IN_KEY=generate_unique_key
NONCE_KEY=generate_unique_key
AUTH_SALT=generate_unique_salt
SECURE_AUTH_SALT=generate_unique_salt
LOGGED_IN_SALT=generate_unique_salt
NONCE_SALT=generate_unique_salt

# AWS
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_DEFAULT_REGION=eu-west-2
```

## Scaling Configuration

### Auto Scaling Settings
```yaml
# .ebextensions/autoscaling.config
option_settings:
  aws:autoscaling:asg:
    MinSize: 2
    MaxSize: 10
  aws:autoscaling:trigger:
    MeasureName: CPUUtilization
    Unit: Percent
    UpperThreshold: 80
    LowerThreshold: 20
```

### Load Balancer Health Checks
- Health check URL: `/health-check.php`
- Healthy threshold: 2
- Unhealthy threshold: 5
- Timeout: 5 seconds
- Interval: 30 seconds

## Monitoring & Maintenance

1. **CloudWatch Alarms**
   - CPU utilization > 80%
   - Database connections > 80%
   - HTTP 5xx errors > 10/minute

2. **Backup Strategy**
   - RDS automated backups (7 days retention)
   - S3 versioning for uploaded files
   - Daily database snapshots

3. **Security**
   - WAF rules for common attacks
   - Security groups restricting database access
   - SSL/TLS encryption in transit and at rest

## Cost Estimation (Monthly)

- **Elastic Beanstalk**: $0 (service fee)
- **EC2 Instances**: ~$50-200 (depending on size/quantity)
- **RDS Database**: ~$25-100 (depending on instance size)
- **S3 Storage**: ~$5-20 (depending on usage)
- **CloudFront**: ~$10-50 (depending on traffic)
- **Total**: ~$90-370/month

## Rollback Plan

1. **DNS Rollback**: Update CloudFlare A records to previous hosting
2. **Database Rollback**: Restore from RDS snapshot
3. **Code Rollback**: `eb deploy` previous version
4. **File Rollback**: Restore from S3 versioned backups

## Docker Configuration for AWS

### Optimized Dockerfile for Production
```dockerfile
FROM php:7.2-apache

# Environment variables
ENV COMPOSER_BRANCH=1
ENV PHP_EXTENSIONS="soap gd mysqli pdo_mysql opcache"
ENV APACHE_DOCUMENT_ROOT=/var/www/html/app/webroot

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git zip unzip curl \
    libfreetype6-dev libjpeg62-turbo-dev libpng-dev \
    && rm -rf /var/lib/apt/lists/*

# Install PHP extensions
RUN docker-php-ext-configure gd --with-freetype-dir=/usr/include/ --with-jpeg-dir=/usr/include/ \
    && docker-php-ext-install -j$(nproc) gd mysqli pdo_mysql soap opcache

# Enable production PHP settings
RUN mv "$PHP_INI_DIR/php.ini-production" "$PHP_INI_DIR/php.ini"

# Configure OPcache for production
RUN echo "opcache.enable=1" >> /usr/local/etc/php/conf.d/opcache.ini \
    && echo "opcache.memory_consumption=256" >> /usr/local/etc/php/conf.d/opcache.ini \
    && echo "opcache.max_accelerated_files=20000" >> /usr/local/etc/php/conf.d/opcache.ini

# Install Composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer --1

# Configure Apache
RUN a2enmod rewrite \
    && sed -ri -e 's!/var/www/html!${APACHE_DOCUMENT_ROOT}!g' /etc/apache2/sites-available/*.conf \
    && sed -ri -e 's!/var/www/!${APACHE_DOCUMENT_ROOT}!g' /etc/apache2/apache2.conf /etc/apache2/conf-available/*.conf

COPY . /var/www/html/
WORKDIR /var/www/html

# Install dependencies and optimize
RUN composer install --no-dev --optimize-autoloader \
    && chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html

EXPOSE 80
```

### Docker Compose for Local Testing
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8080:80"
    environment:
      - RDS_NAME=bon_voyage
      - RDS_USERNAME=root
      - RDS_PASSWORD=password
      - RDS_HOSTNAME=db
      - WP_ENV=development
    depends_on:
      - db
    volumes:
      - ./app/webroot/blog/app/uploads:/var/www/html/app/webroot/blog/app/uploads

  db:
    image: mysql:5.7
    environment:
      MYSQL_DATABASE: bon_voyage
      MYSQL_ROOT_PASSWORD: password
    ports:
      - "3306:3306"
    volumes:
      - db_data:/var/lib/mysql

volumes:
  db_data:
```

## CloudFormation Templates

### RDS Database Stack
```yaml
# infrastructure/rds-stack.yaml
AWSTemplateFormatVersion: '2010-09-09'
Description: 'RDS MySQL database for Bon Voyage'

Parameters:
  DBInstanceClass:
    Type: String
    Default: db.t3.micro
    Description: RDS instance class

  DBAllocatedStorage:
    Type: Number
    Default: 20
    Description: Database storage in GB

Resources:
  DBSubnetGroup:
    Type: AWS::RDS::DBSubnetGroup
    Properties:
      DBSubnetGroupDescription: Subnet group for RDS
      SubnetIds:
        - subnet-51b92838
        - subnet-756dbf0f
        - subnet-de37d792

  DBSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Security group for RDS
      VpcId: vpc-3f2dae57
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 3306
          ToPort: 3306
          SourceSecurityGroupId: !Ref AppSecurityGroup

  AppSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Security group for application
      VpcId: vpc-3f2dae57

  Database:
    Type: AWS::RDS::DBInstance
    Properties:
      DBInstanceIdentifier: bon-voyage-prod
      DBInstanceClass: !Ref DBInstanceClass
      Engine: mysql
      EngineVersion: '8.0'
      AllocatedStorage: !Ref DBAllocatedStorage
      MasterUsername: admin
      MasterUserPassword: !Ref DBPassword
      VPCSecurityGroups:
        - !Ref DBSecurityGroup
      DBSubnetGroupName: !Ref DBSubnetGroup
      BackupRetentionPeriod: 7
      MultiAZ: true
      StorageEncrypted: true

  DBPassword:
    Type: AWS::SSM::Parameter::Value<String>
    Default: /bon-voyage/db-password
    NoEcho: true

Outputs:
  DatabaseEndpoint:
    Description: RDS endpoint
    Value: !GetAtt Database.Endpoint.Address
    Export:
      Name: !Sub "${AWS::StackName}-DatabaseEndpoint"
```

## WordPress Media Storage: S3 + EBS Configuration

### Architecture Overview
WordPress images are stored in S3 but mounted to the application instance's EBS volume for transparent access. This provides:
- **Durability**: S3's 99.999999999% (11 9's) durability
- **Performance**: Local EBS caching for fast access
- **Scalability**: Unlimited S3 storage capacity
- **Cost Efficiency**: S3 storage costs vs EBS volume costs

### S3 Bucket Setup
```bash
# Create S3 bucket for WordPress media
aws s3 mb s3://bon-voyage-wp-media --region eu-west-2

# Configure bucket policy for web access
aws s3api put-bucket-policy --bucket bon-voyage-wp-media --policy '{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "PublicReadGetObject",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::bon-voyage-wp-media/*"
    }
  ]
}'

# Enable versioning for backup protection
aws s3api put-bucket-versioning --bucket bon-voyage-wp-media --versioning-configuration Status=Enabled

# Configure lifecycle policy for cost optimization
aws s3api put-bucket-lifecycle-configuration --bucket bon-voyage-wp-media --lifecycle-configuration '{
  "Rules": [
    {
      "ID": "MediaOptimization",
      "Status": "Enabled",
      "Transitions": [
        {
          "Days": 30,
          "StorageClass": "STANDARD_IA"
        },
        {
          "Days": 90,
          "StorageClass": "GLACIER"
        }
      ]
    }
  ]
}'
```

### S3FS Configuration for EBS Mounting

#### Install S3FS on EC2 Instances
```bash
# .ebextensions/s3fs-setup.config
packages:
  yum:
    fuse: []
    fuse-devel: []
    gcc-c++: []
    git: []
    libcurl-devel: []
    libxml2-devel: []
    openssl-devel: []

commands:
  01_install_s3fs:
    command: |
      cd /tmp
      git clone https://github.com/s3fs-fuse/s3fs-fuse.git
      cd s3fs-fuse
      ./autogen.sh
      ./configure
      make
      make install

  02_create_mount_point:
    command: |
      mkdir -p /var/www/html/app/webroot/blog/app/uploads
      chown www-data:www-data /var/www/html/app/webroot/blog/app/uploads

  03_create_credentials:
    command: |
      echo "${AWS_ACCESS_KEY_ID}:${AWS_SECRET_ACCESS_KEY}" > /etc/passwd-s3fs
      chmod 600 /etc/passwd-s3fs

  04_mount_s3_bucket:
    command: |
      s3fs bon-voyage-wp-media /var/www/html/app/webroot/blog/app/uploads \
        -o passwd_file=/etc/passwd-s3fs \
        -o url=https://s3.eu-west-2.amazonaws.com \
        -o use_cache=/tmp/s3fs-cache \
        -o allow_other \
        -o mp_umask=022 \
        -o uid=33 \
        -o gid=33

  05_add_to_fstab:
    command: |
      echo "s3fs#bon-voyage-wp-media /var/www/html/app/webroot/blog/app/uploads fuse _netdev,allow_other,use_cache=/tmp/s3fs-cache,passwd_file=/etc/passwd-s3fs,url=https://s3.eu-west-2.amazonaws.com,uid=33,gid=33,mp_umask=022 0 0" >> /etc/fstab
```

#### Alternative: Using AWS CLI Sync (Simpler Approach)
```bash
# .ebextensions/s3-sync.config
files:
  "/usr/local/bin/s3-sync-uploads.sh":
    mode: "000755"
    owner: root
    group: root
    content: |
      #!/bin/bash
      UPLOAD_DIR="/var/www/html/app/webroot/blog/app/uploads"
      S3_BUCKET="s3://bon-voyage-wp-media"

      # Sync uploads to S3 every 5 minutes
      while true; do
        aws s3 sync "$UPLOAD_DIR" "$S3_BUCKET" --delete --quiet
        sleep 300
      done

  "/etc/systemd/system/s3-sync.service":
    mode: "000644"
    owner: root
    group: root
    content: |
      [Unit]
      Description=S3 Upload Sync Service
      After=network.target

      [Service]
      Type=simple
      User=root
      ExecStart=/usr/local/bin/s3-sync-uploads.sh
      Restart=always
      RestartSec=10

      [Install]
      WantedBy=multi-user.target

commands:
  01_enable_s3_sync:
    command: |
      systemctl enable s3-sync.service
      systemctl start s3-sync.service
```

### WordPress Configuration for S3 Storage

#### Update wp-config.php
```php
// app/webroot/blog/wp-config.php additions
define('UPLOADS', 'app/uploads');
define('WP_CONTENT_URL', WP_HOME . '/app');

// Optional: Use S3 URLs directly for better performance
if (WP_ENV === 'production') {
    define('WP_CONTENT_URL', 'https://bon-voyage-wp-media.s3.eu-west-2.amazonaws.com');
}
```

#### Install WordPress S3 Plugin (Alternative)
```bash
# Install WP Offload Media plugin for better S3 integration
wp plugin install amazon-s3-and-cloudfront --activate --allow-root

# Configure plugin settings
wp option update tantan_wordpress_s3 '{
  "provider": "aws",
  "access-key-id": "'"$AWS_ACCESS_KEY_ID"'",
  "secret-access-key": "'"$AWS_SECRET_ACCESS_KEY"'",
  "bucket": "bon-voyage-wp-media",
  "region": "eu-west-2",
  "copy-to-s3": "1",
  "serve-from-s3": "1",
  "remove-local-file": "0"
}' --format=json --allow-root
```

### Performance Optimization

#### EBS Volume Configuration
```yaml
# .ebextensions/ebs-optimization.config
option_settings:
  aws:autoscaling:launchconfiguration:
    BlockDeviceMappings: /dev/xvda=:20:true:gp3:3000:125
    # 20GB GP3 volume with 3000 IOPS and 125 MB/s throughput
```

#### S3FS Caching Configuration
```bash
# Optimize S3FS for better performance
s3fs bon-voyage-wp-media /var/www/html/app/webroot/blog/app/uploads \
  -o passwd_file=/etc/passwd-s3fs \
  -o url=https://s3.eu-west-2.amazonaws.com \
  -o use_cache=/tmp/s3fs-cache \
  -o ensure_diskfree=1000 \
  -o parallel_count=10 \
  -o multipart_size=64 \
  -o max_stat_cache_size=100000 \
  -o stat_cache_expire=900 \
  -o enable_noobj_cache \
  -o allow_other
```

### Monitoring and Maintenance

#### CloudWatch Metrics for S3 Storage
```bash
# Monitor S3 bucket usage
aws cloudwatch put-metric-alarm \
  --alarm-name "S3-Storage-Usage" \
  --alarm-description "Monitor S3 storage usage" \
  --metric-name BucketSizeBytes \
  --namespace AWS/S3 \
  --statistic Average \
  --period 86400 \
  --threshold 10737418240 \
  --comparison-operator GreaterThanThreshold \
  --dimensions Name=BucketName,Value=bon-voyage-wp-media Name=StorageType,Value=StandardStorage
```

#### Backup and Sync Verification
```bash
# Daily verification script
#!/bin/bash
# /usr/local/bin/verify-s3-sync.sh

UPLOAD_DIR="/var/www/html/app/webroot/blog/app/uploads"
S3_BUCKET="s3://bon-voyage-wp-media"

# Count local files
LOCAL_COUNT=$(find "$UPLOAD_DIR" -type f | wc -l)

# Count S3 objects
S3_COUNT=$(aws s3 ls "$S3_BUCKET" --recursive | wc -l)

# Log discrepancies
if [ "$LOCAL_COUNT" -ne "$S3_COUNT" ]; then
    echo "$(date): File count mismatch - Local: $LOCAL_COUNT, S3: $S3_COUNT" >> /var/log/s3-sync.log
    # Trigger sync
    aws s3 sync "$UPLOAD_DIR" "$S3_BUCKET" --delete
fi
```

### Cost Optimization

#### Storage Class Analysis
- **Standard**: First 30 days (frequent access)
- **Standard-IA**: 30-90 days (infrequent access)
- **Glacier**: 90+ days (archive)

#### Estimated Monthly Costs
```
S3 Storage (100GB):
- Standard (30 days): $2.30
- Standard-IA (60 days): $1.25
- Glacier (archive): $0.40
- Total S3: ~$4.00/month

EBS Volume (20GB GP3): ~$2.00/month
Data Transfer: ~$1.00/month

Total Storage Cost: ~$7.00/month
```

## Security Considerations

### IAM Roles and Policies
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject"
      ],
      "Resource": "arn:aws:s3:::bon-voyage-assets-prod/*"
    },
    {
      "Effect": "Allow",
      "Action": [
        "s3:ListBucket"
      ],
      "Resource": "arn:aws:s3:::bon-voyage-assets-prod"
    },
    {
      "Effect": "Allow",
      "Action": [
        "rds:DescribeDBInstances"
      ],
      "Resource": "*"
    }
  ]
}
```

### Security Headers Configuration
```apache
# .htaccess additions for security
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
Header always set Referrer-Policy "strict-origin-when-cross-origin"
Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'"
```

## Performance Optimization

### PHP Configuration for Production
```ini
; php.ini optimizations
memory_limit = 256M
max_execution_time = 30
max_input_time = 60
post_max_size = 32M
upload_max_filesize = 32M
max_file_uploads = 20

; OPcache settings
opcache.enable = 1
opcache.memory_consumption = 256
opcache.interned_strings_buffer = 16
opcache.max_accelerated_files = 20000
opcache.revalidate_freq = 2
opcache.fast_shutdown = 1
```

### Database Optimization
```sql
-- MySQL configuration recommendations
SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB for t3.micro
SET GLOBAL query_cache_size = 67108864; -- 64MB
SET GLOBAL query_cache_type = 1;
SET GLOBAL slow_query_log = 1;
SET GLOBAL long_query_time = 2;
```

## Troubleshooting Guide

### Common Issues and Solutions

1. **Database Connection Errors**
   ```bash
   # Check RDS security groups
   aws ec2 describe-security-groups --group-ids sg-xxxxxxxxx

   # Test database connection
   mysql -h your-rds-endpoint -u admin -p
   ```

2. **File Upload Issues**
   ```bash
   # Check EFS mount
   df -h | grep efs

   # Test file permissions
   ls -la /resources/
   ```

3. **SSL Certificate Problems**
   ```bash
   # Check certificate status
   aws acm list-certificates --region eu-west-2

   # Verify CloudFlare SSL settings
   curl -I https://www.bon-voyage.co.uk
   ```

4. **Performance Issues**
   ```bash
   # Check CloudWatch metrics
   aws cloudwatch get-metric-statistics \
     --namespace AWS/ApplicationELB \
     --metric-name TargetResponseTime \
     --start-time 2024-01-01T00:00:00Z \
     --end-time 2024-01-02T00:00:00Z \
     --period 3600 \
     --statistics Average
   ```

## CakePHP 1.2.12 Migration Feasibility Analysis

### Current Application Audit

#### Core Functionality Identified:
1. **Travel Planning System**
   - Complex multi-step travel plan forms with validation
   - Integration with external CRM system (AddQuoteRequest_UTM API)
   - ReCaptcha v3 integration for spam protection
   - Email newsletter subscription system

2. **Content Management**
   - Hierarchical destination management (USA/Canada with regions)
   - Holiday types and itineraries
   - Image management with multiple versions/crops
   - Page management with navigation trees
   - Spotlight/featured content system

3. **User Authentication & Authorization**
   - ACL-based permission system
   - Admin interface (/webadmin) with role-based access
   - Session management and history tracking

4. **Third-Party Integrations**
   - **Feefo Reviews**: XML API integration for customer reviews
   - **Google APIs**: Custom search and ReCaptcha
   - **Email Services**: Custom email list subscription service
   - **CRM Integration**: SOAP/REST API for lead management

5. **Custom Components & Features**
   - Image processing and versioning system
   - Navigation component with mega menu support
   - Filter component for search/listing pages
   - History component for breadcrumb navigation
   - Custom caching system

#### Database Schema Complexity:
- **50+ tables** including destinations, accommodation, itineraries, pages, users
- **Complex relationships**: Tree structures, HABTM associations
- **Custom fields**: Meta data, SEO fields, publishing workflow
- **File attachments**: Image associations with versioning

### Migration Path Analysis

#### Option 1: CakePHP 1.2.12 → PHP 7.4/8.1 (Direct Migration)

**Feasibility: ❌ NOT RECOMMENDED**

**Critical Blockers:**
```php
// CakePHP 1.2.12 uses deprecated PHP features
var $name = 'Model';           // PHP 7.4+ deprecates var keyword
var $components = array();     // Old array syntax
mysql_* functions              // Removed in PHP 7.0
split() function              // Removed in PHP 7.0
ereg_* functions              // Removed in PHP 7.0
```

**Major Compatibility Issues:**
1. **PHP Language Changes**
   - `var` keyword deprecated → use `public/private/protected`
   - `mysql_*` functions removed → must use `mysqli_*` or PDO
   - `split()` function removed → use `explode()` or `preg_split()`
   - Constructor method changes (`__construct()` vs class name)

2. **CakePHP 1.2.12 Framework Issues**
   - No official PHP 7+ support
   - Core framework uses deprecated functions
   - Security vulnerabilities (no updates since 2012)
   - Incompatible with modern PHP error handling

**Estimated Effort: 6-12 months**
- Rewrite 80% of framework core files
- Update all models, controllers, and views
- Extensive testing and debugging
- High risk of introducing bugs

#### Option 2: Rebuild in WordPress + Gravity Forms + ACF

**Feasibility: ✅ HIGHLY RECOMMENDED**

**Advantages:**
- **Content Management**: WordPress excels at content management
- **Form Handling**: Gravity Forms handles complex multi-step forms
- **Custom Fields**: ACF provides flexible field management
- **Existing Integration**: Blog already uses WordPress
- **Maintenance**: Easier to find WordPress developers

**Implementation Strategy:**
```php
// Travel Plan Form (Gravity Forms)
add_action('gform_after_submission_1', 'handle_travel_plan_submission', 10, 2);

function handle_travel_plan_submission($entry, $form) {
    // Integrate with existing CRM API
    $crm_data = prepare_crm_data($entry);
    send_to_crm($crm_data);

    // Newsletter subscription
    if ($entry['email_consent']) {
        subscribe_to_newsletter($entry['email'], $entry['first_name'], $entry['last_name']);
    }
}

// Custom Post Types for Destinations
register_post_type('destination', [
    'public' => true,
    'hierarchical' => true,
    'supports' => ['title', 'editor', 'thumbnail', 'custom-fields']
]);

// ACF Fields for destination data
acf_add_local_field_group([
    'key' => 'destination_fields',
    'title' => 'Destination Information',
    'fields' => [
        ['key' => 'map_latitude', 'name' => 'map_latitude', 'type' => 'number'],
        ['key' => 'map_longitude', 'name' => 'map_longitude', 'type' => 'number'],
        ['key' => 'from_price', 'name' => 'from_price', 'type' => 'number']
    ]
]);
```

**Migration Mapping:**
- **Destinations** → Custom Post Type with ACF fields
- **Travel Plans** → Gravity Forms with custom submission handling
- **User Management** → WordPress users with custom roles
- **Image Management** → WordPress media library with custom sizes
- **Navigation** → WordPress menus with custom walker

**Estimated Effort: 3-4 months**
- Custom post types and fields: 2 weeks
- Form migration to Gravity Forms: 3 weeks
- Template development: 4 weeks
- API integrations: 2 weeks
- Testing and refinement: 4 weeks

#### Option 3: Rebuild in Laravel

**Feasibility: ✅ RECOMMENDED (Technical Excellence)**

**Advantages:**
- **Modern Framework**: Laravel 10+ with PHP 8.1+ support
- **Robust Architecture**: MVC, Eloquent ORM, Artisan CLI
- **Rich Ecosystem**: Packages for forms, authentication, APIs
- **Performance**: Better caching, queue system, optimization
- **Security**: Built-in CSRF, XSS protection, authentication

**Implementation Strategy:**
```php
// Laravel Models
class Destination extends Model {
    use HasMany, BelongsToMany;

    protected $fillable = ['name', 'slug', 'latitude', 'longitude', 'published'];

    public function children() {
        return $this->hasMany(Destination::class, 'parent_id');
    }

    public function holidayTypes() {
        return $this->belongsToMany(HolidayType::class);
    }
}

// Form Requests for Validation
class TravelPlanRequest extends FormRequest {
    public function rules() {
        return [
            'first_name' => 'required|string|max:255',
            'email_address' => 'required|email',
            'destination_country' => 'required|in:USA,Canada',
            'num_adults' => 'required|integer|min:1|max:20'
        ];
    }
}

// API Integration Service
class CrmService {
    public function createTravelPlan(array $data): string {
        $response = Http::post(config('services.crm.endpoint'), [
            'userName' => config('services.crm.username'),
            'password' => config('services.crm.password'),
            'FirstName' => $data['first_name'],
            'Email' => $data['email_address']
        ]);

        return $response->json('url');
    }
}
```

**Migration Benefits:**
- **Database Migrations**: Version-controlled schema changes
- **Eloquent ORM**: Elegant database relationships
- **Form Validation**: Built-in validation with custom rules
- **API Integration**: HTTP client with retry logic
- **Testing**: PHPUnit integration for comprehensive testing
- **Deployment**: Laravel Forge/Vapor for easy AWS deployment

**Estimated Effort: 4-6 months**
- Database design and migrations: 3 weeks
- Model and relationship setup: 2 weeks
- Controller and API development: 6 weeks
- Frontend template development: 6 weeks
- Third-party integrations: 3 weeks
- Testing and optimization: 4 weeks

### Recommendation Matrix

| Criteria | CakePHP Migration | WordPress + GF + ACF | Laravel |
|----------|-------------------|---------------------|---------|
| **Development Time** | 6-12 months | 3-4 months | 4-6 months |
| **Technical Risk** | ❌ Very High | ✅ Low | ✅ Medium |
| **Maintenance Cost** | ❌ High | ✅ Low | ✅ Medium |
| **Developer Availability** | ❌ Very Limited | ✅ High | ✅ High |
| **Future Scalability** | ❌ Poor | ✅ Good | ✅ Excellent |
| **Security Updates** | ❌ None | ✅ Regular | ✅ Regular |
| **Performance** | ❌ Poor | ✅ Good | ✅ Excellent |
| **Total Cost** | ❌ $150k-300k | ✅ $60k-80k | ✅ $80k-120k |

### Final Recommendation

**Primary Choice: WordPress + Gravity Forms + ACF**
- Fastest implementation (3-4 months)
- Lowest risk and cost
- Leverages existing WordPress blog
- Easy to maintain and extend
- Large developer pool available

**Alternative Choice: Laravel (for long-term technical excellence)**
- Modern, maintainable codebase
- Better performance and scalability
- Excellent testing capabilities
- Future-proof architecture

**Avoid: Direct CakePHP Migration**
- Extremely high risk and cost
- No security updates available
- Limited developer expertise
- Poor return on investment

## Database Migration Scripting Analysis

### Current Database Schema Overview

The CakePHP application uses a complex database schema with **30+ core tables** and extensive relationships:

#### **Core Content Tables:**
- `destinations` (589 records) - Hierarchical tree structure with lft/rght
- `accommodation` (671 records) - Hotels/lodging with complex relationships
- `activities` (719 records) - Things to do at destinations
- `holiday_types` (23 records) - Travel categories/themes
- `itineraries` (197 records) - Multi-day travel plans
- `pages` (60 records) - CMS pages with tree structure
- `spotlights` (250 records) - Featured content/offers

#### **Relationship Tables (Many-to-Many):**
- `accommodation_destinations` (3,087 records)
- `activities_destinations` (3,791 records)
- `destinations_itineraries` (5,625 records)
- `holiday_types_on_destinations` (671 records)
- `accommodation_holiday_types` (741 records)

#### **Media & Content Management:**
- `images` (7,887 records) - File management system
- `image_versions` (8 records) - Different image sizes/crops
- `content_blocks` (4,986 records) - Flexible content blocks
- `custom_image_versions` - Custom image cropping data

#### **User & Form Data:**
- `users` (27 records) - Admin users with ACL
- `travel_plans` (18 records) - Lead generation forms
- `quote_requests` (8,981 records) - Legacy quote forms
- `contacts` (3,294 records) - Contact form submissions

#### **Complex Features:**
- **ACL System**: `acos`, `aros`, `aros_acos` tables for permissions
- **Tree Structures**: Nested set model (lft/rght) for hierarchical data
- **Image Management**: Custom versioning and cropping system
- **Content Blocks**: Polymorphic content system

### WordPress + ACF Migration Script Feasibility

#### ✅ **HIGHLY FEASIBLE** - Automated Conversion Possible

**Migration Strategy:**
```php
<?php
// WordPress Migration Script Structure

class BonVoyageMigrationScript {

    public function migrate() {
        $this->setupWordPressStructure();
        $this->migrateUsers();
        $this->migrateImages();
        $this->migrateDestinations();
        $this->migrateAccommodation();
        $this->migrateActivities();
        $this->migrateHolidayTypes();
        $this->migrateItineraries();
        $this->migratePages();
        $this->migrateSpotlights();
        $this->migrateTravelPlans();
        $this->setupRelationships();
        $this->migrateContentBlocks();
    }

    private function setupWordPressStructure() {
        // Register Custom Post Types
        $this->registerPostType('destination', [
            'hierarchical' => true,
            'supports' => ['title', 'editor', 'thumbnail', 'page-attributes']
        ]);

        $this->registerPostType('accommodation');
        $this->registerPostType('activity');
        $this->registerPostType('holiday_type');
        $this->registerPostType('itinerary');
        $this->registerPostType('spotlight');

        // Register ACF Field Groups
        $this->setupACFFields();
    }
}
```

#### **1. User Migration (✅ Simple)**
```sql
-- Direct mapping possible
INSERT INTO wp_users (user_login, user_email, user_pass, user_registered)
SELECT email, email, password, created
FROM users;

-- Map to WordPress roles
INSERT INTO wp_usermeta (user_id, meta_key, meta_value)
SELECT wp_user_id, 'wp_capabilities',
  CASE WHEN group_id = 1 THEN 'a:1:{s:13:"administrator";b:1;}'
       ELSE 'a:1:{s:6:"editor";b:1;}'
  END
FROM user_mapping;
```

#### **2. Image Migration (✅ Automated)**
```php
function migrateImages() {
    $images = $this->db->query("SELECT * FROM images WHERE published = 1");

    foreach ($images as $image) {
        // Copy physical files
        $oldPath = "/old/img/{$image['id']}.{$image['extension']}";
        $newPath = wp_upload_dir()['path'] . "/{$image['id']}.{$image['extension']}";
        copy($oldPath, $newPath);

        // Create WordPress attachment
        $attachment_id = wp_insert_attachment([
            'post_title' => $image['alt'],
            'post_content' => '',
            'post_status' => 'inherit',
            'post_mime_type' => 'image/' . $image['extension']
        ], $newPath);

        // Store mapping for relationships
        $this->imageMapping[$image['id']] = $attachment_id;
    }
}
```

#### **3. Destinations Migration (✅ Complex but Doable)**
```php
function migrateDestinations() {
    // Handle hierarchical structure
    $destinations = $this->db->query("
        SELECT * FROM destinations
        WHERE published = 1
        ORDER BY lft ASC
    ");

    foreach ($destinations as $dest) {
        $post_id = wp_insert_post([
            'post_title' => $dest['name'],
            'post_name' => $dest['slug'],
            'post_content' => $dest['summary'],
            'post_type' => 'destination',
            'post_status' => 'publish',
            'post_parent' => $this->getWordPressParent($dest['parent_id'])
        ]);

        // Migrate ACF fields
        update_field('latitude', $dest['latitude'], $post_id);
        update_field('longitude', $dest['longitude'], $post_id);
        update_field('meta_description', $dest['meta_description'], $post_id);
        update_field('youtube_playlist_id', $dest['youtube_playlist_id'], $post_id);

        // Set featured image
        if ($dest['main_image_id']) {
            set_post_thumbnail($post_id, $this->imageMapping[$dest['main_image_id']]);
        }

        $this->destinationMapping[$dest['id']] = $post_id;
    }
}
```

#### **4. Content Blocks Migration (✅ Flexible Content)**
```php
function migrateContentBlocks() {
    $blocks = $this->db->query("
        SELECT * FROM content_blocks
        ORDER BY model, modelid, `order`
    ");

    $grouped = [];
    foreach ($blocks as $block) {
        $grouped[$block['model']][$block['modelid']][] = $block;
    }

    foreach ($grouped as $model => $modelBlocks) {
        foreach ($modelBlocks as $modelId => $blocks) {
            $postId = $this->getWordPressPostId($model, $modelId);

            $flexibleContent = [];
            foreach ($blocks as $block) {
                $flexibleContent[] = [
                    'acf_fc_layout' => 'content_block',
                    'content' => $block['content'],
                    'image' => $this->imageMapping[$block['image_id']] ?? null,
                    'alignment' => $block['alignment'],
                    'link_text' => $block['link_text'],
                    'link' => $block['link'],
                    'youtube_video_id' => $block['youtube_video_id']
                ];
            }

            update_field('content_blocks', $flexibleContent, $postId);
        }
    }
}
```

#### **5. Relationship Migration (✅ Post-to-Post Relationships)**
```php
function setupRelationships() {
    // Accommodation to Destinations
    $relationships = $this->db->query("
        SELECT accommodation_id, destination_id, featured, `order`
        FROM accommodation_destinations
    ");

    foreach ($relationships as $rel) {
        $accommodationPost = $this->accommodationMapping[$rel['accommodation_id']];
        $destinationPost = $this->destinationMapping[$rel['destination_id']];

        // Use ACF Relationship field
        $currentDestinations = get_field('destinations', $accommodationPost) ?: [];
        $currentDestinations[] = $destinationPost;
        update_field('destinations', $currentDestinations, $accommodationPost);

        // Store featured/order metadata
        if ($rel['featured']) {
            add_post_meta($accommodationPost, 'featured_destination_' . $destinationPost, true);
        }
    }
}
```

#### **6. Travel Plans Migration (✅ Gravity Forms Integration)**
```php
function migrateTravelPlans() {
    // Create Gravity Form programmatically
    $form = $this->createTravelPlanForm();

    // Migrate existing submissions as form entries
    $travelPlans = $this->db->query("SELECT * FROM travel_plans");

    foreach ($travelPlans as $plan) {
        $entry = [
            'form_id' => $form['id'],
            'date_created' => $plan['created'],
            'is_starred' => 0,
            'is_read' => 1,
            'ip' => '127.0.0.1',
            'source_url' => 'https://www.bon-voyage.co.uk/travel_plans/add',
            'user_agent' => 'Migration Script',
            '1' => $plan['title'],
            '2' => $plan['first_name'],
            '3' => $plan['last_name'],
            '4' => $plan['email_address'],
            '5' => $plan['telephone_number'],
            // ... map all fields
        ];

        GFAPI::add_entry($entry);
    }
}
```

### Migration Script Structure

#### **Complete Migration Script:**
```bash
#!/bin/bash
# migration.sh - Complete database migration script

echo "Starting Bon Voyage CakePHP to WordPress Migration..."

# 1. Backup existing WordPress database
wp db export backup-pre-migration.sql

# 2. Run PHP migration script
php migrate.php --step=setup
php migrate.php --step=users
php migrate.php --step=images
php migrate.php --step=content
php migrate.php --step=relationships
php migrate.php --step=forms

# 3. Update WordPress URLs and flush rewrite rules
wp search-replace 'old-domain.com' 'new-domain.com'
wp rewrite flush

# 4. Regenerate image thumbnails
wp media regenerate

echo "Migration completed successfully!"
```

### Migration Complexity Assessment

| Component | Complexity | Automation Level | Estimated Time |
|-----------|------------|------------------|----------------|
| **Users & Permissions** | ✅ Low | 95% Automated | 2 hours |
| **Images & Media** | ✅ Medium | 90% Automated | 1 day |
| **Destinations (Hierarchical)** | ⚠️ Medium | 85% Automated | 2 days |
| **Accommodation** | ✅ Medium | 90% Automated | 1 day |
| **Activities & Holiday Types** | ✅ Low | 95% Automated | 4 hours |
| **Itineraries** | ⚠️ Medium | 80% Automated | 1 day |
| **Content Blocks** | ⚠️ Complex | 75% Automated | 2 days |
| **Many-to-Many Relationships** | ⚠️ Complex | 70% Automated | 2 days |
| **Travel Plans → Gravity Forms** | ✅ Medium | 85% Automated | 1 day |
| **URL Redirects & SEO** | ✅ Medium | 90% Automated | 4 hours |

### **Total Migration Effort: 10-12 days**

#### **Success Factors:**
- ✅ **Clean Data Structure**: Well-normalized database
- ✅ **Standard Patterns**: Common CMS patterns (hierarchies, relationships)
- ✅ **WordPress Flexibility**: ACF handles complex field structures
- ✅ **Existing Tools**: WP-CLI, ACF Pro, Gravity Forms APIs

#### **Risk Mitigation:**
- **Staging Environment**: Test migration multiple times
- **Data Validation**: Compare record counts and relationships
- **Rollback Plan**: Database backups at each step
- **Manual QA**: Review critical content and functionality

### **Recommendation: Proceed with Automated Migration**

The database structure is **highly suitable for automated migration** to WordPress + ACF. The well-organized schema, standard relationships, and WordPress's flexible content system make this a **low-risk, high-success** migration path.

## Blog Migration: Bedrock/Timber → Vanilla WordPress

### Current Blog Architecture Analysis

The existing blog uses a **modern but complex** setup:

#### **Current Stack:**
- **Bedrock**: WordPress boilerplate with Composer dependency management
- **Timber**: Twig templating engine for WordPress
- **Lumberjack**: Object-oriented framework built on Timber
- **Custom Structure**: Non-standard directory layout

#### **Current Blog Structure:**
```
app/webroot/blog/
├── wp/                    # WordPress core (via Composer)
├── app/
│   ├── mu-plugins/        # Must-use plugins (Timber)
│   ├── plugins/           # Standard plugins
│   ├── themes/bonvoyage/  # Custom theme
│   │   ├── views/         # Twig templates
│   │   ├── lumberjack/    # Framework files
│   │   └── assets/        # CSS/JS assets
│   └── uploads/           # Media files
└── wp-config.php          # WordPress configuration
```

#### **Template Structure (Twig):**
- `base.twig` - Main layout template
- `posts.twig` - Blog listing page
- `single.twig` - Individual post template
- `components/` - Reusable template components

#### **Current Functionality:**
- ✅ **Simple Blog**: Standard WordPress posts and pages
- ✅ **Custom Styling**: Matches main site design
- ✅ **Navigation Integration**: Shared header/footer with main site
- ✅ **Search Functionality**: Custom search toggle implementation
- ✅ **Social Sharing**: Twitter/Facebook integration
- ✅ **Feefo Reviews**: Customer review integration

### Migration Options Analysis

#### **Option 1: Keep Bedrock/Timber (✅ Minimal Effort)**

**Feasibility**: ✅ **SIMPLE** - No migration needed
**Effort**: 1-2 days for AWS deployment optimization

**Pros:**
- No migration required
- Modern development workflow
- Twig templating is clean and maintainable
- Composer dependency management

**Cons:**
- Complex deployment (Composer dependencies)
- Non-standard WordPress structure
- Requires developer familiarity with Timber/Twig
- Harder to find WordPress developers

**Implementation:**
```bash
# Deployment considerations for Bedrock
composer install --no-dev --optimize-autoloader
wp core update
wp plugin update --all
```

#### **Option 2: Migrate to Vanilla WordPress (✅ RECOMMENDED)**

**Feasibility**: ✅ **HIGHLY FEASIBLE** - Simple blog structure
**Effort**: 3-5 days for complete migration

**Pros:**
- Standard WordPress structure
- Easier maintenance and updates
- Broader developer availability
- Simpler deployment process
- Better plugin compatibility

**Cons:**
- Requires template conversion
- Loss of Twig templating benefits
- Need to recreate custom functionality

### Vanilla WordPress Migration Strategy

#### **1. Template Conversion (Twig → PHP)**

**Current Twig Template:**
```twig
{% extends "base.twig" %}
{% block content %}
    {% for post in posts %}
        <div class="entry">
            <h1 class="entry__hd">
                <a href="{{ post.permalink }}">{{ post.title }}</a>
            </h1>
            {{ post.get_preview(50, false, 'Read More...', true) }}
        </div>
    {% endfor %}
{% endblock %}
```

**Converted PHP Template:**
```php
<?php get_header(); ?>
<div class="page-content-body__content page-content-body__content--blog">
    <div class="section-content-wrapper">
        <div class="section-content section-content--blog">
            <div class="section-content__inner section-content__inner--blog">
                <?php if (have_posts()) : ?>
                    <?php while (have_posts()) : the_post(); ?>
                        <div class="entry">
                            <div class="entry__content">
                                <h1 class="entry__hd">
                                    <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                </h1>
                                <?php if (has_post_thumbnail()) : ?>
                                    <div class="entry__thumbnail">
                                        <?php the_post_thumbnail('medium'); ?>
                                    </div>
                                <?php endif; ?>
                                <ul class="entry__meta entry__meta--date-author">
                                    <li>Published on <a href="<?php the_permalink(); ?>"><?php the_date(); ?></a></li>
                                    <li>Author <a href="<?php echo get_author_posts_url(get_the_author_meta('ID')); ?>"><?php the_author(); ?></a></li>
                                </ul>
                                <p><?php echo wp_trim_words(get_the_content(), 50, '... <a href="' . get_permalink() . '">Read More</a>'); ?></p>
                            </div>
                        </div>
                    <?php endwhile; ?>

                    <?php the_posts_pagination(array(
                        'prev_text' => '&laquo; Previous',
                        'next_text' => 'Next &raquo;',
                    )); ?>
                <?php else : ?>
                    <p>No results found.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php get_footer(); ?>
```

#### **2. Directory Structure Migration**

**Target Vanilla WordPress Structure:**
```
wp-content/
├── themes/bonvoyage/
│   ├── index.php          # Main template
│   ├── header.php         # Header template
│   ├── footer.php         # Footer template
│   ├── single.php         # Single post template
│   ├── archive.php        # Archive template
│   ├── search.php         # Search results
│   ├── 404.php           # Error page
│   ├── functions.php      # Theme functions
│   ├── style.css         # Main stylesheet
│   └── assets/           # CSS/JS/Images
├── plugins/              # Standard plugins
└── uploads/              # Media files
```

#### **3. Migration Script**

```php
<?php
// blog-migration.php - Bedrock to Vanilla WordPress Migration

class BlogMigrationScript {

    public function migrate() {
        $this->setupVanillaStructure();
        $this->convertTemplates();
        $this->migrateFunctionality();
        $this->updateConfiguration();
        $this->testMigration();
    }

    private function setupVanillaStructure() {
        // Create standard WordPress directory structure
        $this->createDirectories([
            'wp-content/themes/bonvoyage',
            'wp-content/themes/bonvoyage/assets/css',
            'wp-content/themes/bonvoyage/assets/js',
            'wp-content/themes/bonvoyage/assets/img'
        ]);
    }

    private function convertTemplates() {
        $templates = [
            'base.twig' => 'header.php + footer.php',
            'posts.twig' => 'index.php',
            'single.twig' => 'single.php',
            'archive.twig' => 'archive.php',
            'search.twig' => 'search.php',
            '404.twig' => '404.php'
        ];

        foreach ($templates as $twig => $php) {
            $this->convertTemplate($twig, $php);
        }
    }

    private function migrateFunctionality() {
        // Convert Timber/Lumberjack functionality to standard WordPress
        $this->migrateSearchToggle();
        $this->migrateSocialSharing();
        $this->migrateFeefoIntegration();
        $this->migrateNavigationIntegration();
    }
}
```

#### **4. Key Functionality Migration**

**Search Toggle (Already Implemented):**
```php
// Current implementation in functions.php is already vanilla WordPress
function bonvoyage_enqueue_assets() {
    wp_enqueue_script('bonvoyage-search-toggle',
        get_template_directory_uri() . '/assets/js/search-toggle.js');
}
add_action('wp_enqueue_scripts', 'bonvoyage_enqueue_assets');
```

**Navigation Integration:**
```php
// functions.php - Integrate with main site navigation
function bonvoyage_shared_navigation() {
    // Load navigation from main site endpoints
    $megamenu = wp_remote_get(home_url('/megamenu'));
    $mmenu = wp_remote_get(home_url('/mmenu'));

    if (!is_wp_error($megamenu)) {
        set_transient('bonvoyage_megamenu', wp_remote_retrieve_body($megamenu), HOUR_IN_SECONDS);
    }

    if (!is_wp_error($mmenu)) {
        set_transient('bonvoyage_mmenu', wp_remote_retrieve_body($mmenu), HOUR_IN_SECONDS);
    }
}
add_action('init', 'bonvoyage_shared_navigation');
```

### Design Migration Considerations

#### **Option A: Direct Design Migration (✅ SIMPLE)**
- **Effort**: 2-3 days
- **Approach**: Copy existing CSS/HTML structure exactly
- **Benefit**: Maintains current design consistency
- **Risk**: Low - proven design

#### **Option B: Design Refresh During Migration (⚠️ COMPLEX)**
- **Effort**: 2-3 weeks
- **Approach**: Redesign blog to match updated main site
- **Benefit**: Opportunity for improvement
- **Risk**: Medium - scope creep potential

### Migration Timeline & Effort

| Task | Complexity | Time | Dependencies |
|------|------------|------|--------------|
| **Template Conversion** | ✅ Medium | 2 days | Design decision |
| **Functionality Migration** | ✅ Low | 1 day | Template completion |
| **Testing & QA** | ✅ Low | 1 day | All components |
| **Design Updates (Optional)** | ⚠️ High | 2-3 weeks | Design approval |
| **Total (Direct Migration)** | | **4-5 days** | |
| **Total (With Redesign)** | | **3-4 weeks** | |

### Recommendation: Vanilla WordPress Migration

**Primary Choice: Direct Migration (4-5 days)**
- Maintain current design exactly
- Convert Twig templates to PHP
- Preserve all existing functionality
- Standard WordPress deployment

**Alternative: Migration + Redesign (3-4 weeks)**
- Opportunity to refresh blog design
- Align with any main site updates
- Requires design approval and additional budget

**Benefits of Vanilla WordPress:**
- ✅ **Simpler Deployment**: Standard WordPress hosting
- ✅ **Easier Maintenance**: No Composer dependencies
- ✅ **Better Compatibility**: Standard plugin ecosystem
- ✅ **Developer Availability**: Larger talent pool
- ✅ **Lower Risk**: Proven, stable platform

The blog migration is **highly feasible** and can be completed in **4-5 days** for a direct conversion, maintaining the current design and functionality while moving to a standard WordPress structure.

## Current AWS Infrastructure Analysis (From Off-boarding Documentation)

### Existing Production Environment

Based on the off-boarding documentation, the current AWS setup includes:

#### **Elastic Beanstalk Configuration:**
- **Environment Name**: `Bvwww-env-1`
- **Platform**: PHP 7.2 on Amazon Linux
- **Auto Scaling**: 1-2 instances, scales at 70% CPU usage
- **Region**: eu-west-2 (London) - *inferred from VPC configuration*

#### **Current VPC and Network Configuration:**
```yaml
VPC ID: vpc-3f2dae57
Subnets:
  - subnet-51b92838 (AZ A)
  - subnet-756dbf0f (AZ B)
  - subnet-de37d792 (AZ C)
Security Group: sg-0e826ec64a13a2a54
```

#### **Storage and CDN:**
- **EFS**: Mounted to `/resources` for shared file storage
- **S3 Bucket**: `bv-www-resources` (synced via cron every minute)
- **CloudFront CDN**:
  - CSS/JS: `assets.bon-voyage.co.uk`
  - Images: `resources.bon-voyage.co.uk`
- **RDS Database**: Amazon RDS (MySQL compatible)

#### **Current Deployment Process:**
```bash
# Build assets (Node 18.10.0, Ruby 2.6.3)
bundle install && npm install && npm run build

# Create deployment package
./create_zip.sh

# Deploy via EB CLI
eb use Bvwww-env-1
eb deploy Bvwww-env-1
```

#### **Technology Stack:**
- **CakePHP**: 1.2.12 (forked version for PHP 7.2 compatibility)
- **PHP**: 7.2 (⚠️ **CRITICAL**: No longer supported by AWS)
- **Composer**: Version 1
- **Node.js**: 0.12 (⚠️ **CRITICAL**: Extremely outdated)
- **Build Tools**: NPM, Bower, Gulp

### Critical Infrastructure Risks Identified

#### **⚠️ URGENT: Platform End-of-Life Issues**
1. **PHP 7.2**: No longer supported by AWS Elastic Beanstalk
2. **Node.js 0.12**: Severe security vulnerabilities, no updates since 2016
3. **CakePHP 1.2.12**: No security updates since 2012
4. **Amazon Linux AMI**: Legacy images may be removed within 2 years

#### **Security and Operational Risks:**
- **No Staging Environment**: All changes deployed directly to production
- **Bot Attacks**: Increased frequency, currently mitigated but ongoing concern
- **CloudFlare Integration**: Considered but deemed too risky due to DNS complexity
- **Manual Deployment**: No CI/CD after Rareloop contract ends

## Developer Profile and Project Timing Assumptions

### **Developer Skill Requirements**

The project timings in this guide are based on a **Senior Full-Stack Developer** with the following profile:

#### **Required Technical Skills:**
- **5+ years PHP development experience**
- **3+ years WordPress development** (themes, plugins, ACF)
- **2+ years AWS experience** (EC2, RDS, S3, CloudFront)
- **Database migration experience** (MySQL, data transformation)
- **Modern development workflow** (Git, CI/CD, Docker)

#### **Specific Technology Experience:**
- **Legacy PHP Migration**: Experience with PHP 7.2+ upgrades
- **CakePHP Knowledge**: Understanding of MVC patterns and ORM
- **WordPress Architecture**: Custom post types, ACF, Gravity Forms
- **AWS Services**: Elastic Beanstalk, RDS, EFS, S3, CloudFront
- **Frontend Build Tools**: NPM, Webpack, modern CSS/JS workflows

#### **Project Management Skills:**
- **Database Migration Planning**: Schema analysis and data mapping
- **Risk Assessment**: Understanding of production deployment risks
- **Testing Methodologies**: Staging environment setup and QA processes
- **Documentation**: Technical documentation and handover processes

### **Time Estimates Based On Developer Experience Level**

| Task Category | Senior Developer (5+ years) | Mid-Level (2-4 years) | Junior (0-2 years) |
|---------------|------------------------------|----------------------|-------------------|
| **CakePHP Analysis** | 2-3 days | 5-7 days | 10-14 days |
| **Database Migration Script** | 5-7 days | 10-14 days | 20-25 days |
| **WordPress Setup** | 3-4 days | 7-10 days | 14-18 days |
| **AWS Infrastructure** | 2-3 days | 5-7 days | 10-14 days |
| **Testing & QA** | 3-4 days | 5-7 days | 8-10 days |
| **Total Project Time** | **15-21 days** | **32-45 days** | **62-81 days** |

### **Additional Considerations for Timeline**

#### **Factors That May Extend Timeline:**
- **Legacy Code Complexity**: CakePHP 1.2.12 has non-standard patterns
- **Data Volume**: 30+ tables with complex relationships may require additional testing
- **Third-Party Integrations**: Feefo, Google APIs, CRM systems need verification
- **No Staging Environment**: Requires careful production deployment planning
- **CloudFront Configuration**: CDN setup and DNS changes add complexity

#### **Risk Mitigation Requirements:**
- **Backup Strategy**: Full database and file system backups before migration
- **Rollback Plan**: Ability to restore previous environment within 1 hour
- **Monitoring Setup**: Replace Rareloop's monitoring with new solution
- **Security Audit**: Review and update all access credentials and SSH keys

### **Recommended Team Structure**

For optimal project execution:

#### **Core Team (Minimum):**
- **1x Senior Full-Stack Developer** (Lead)
- **1x DevOps/AWS Specialist** (Infrastructure)
- **1x QA Tester** (Testing and validation)

#### **Extended Team (Recommended):**
- **1x Project Manager** (Coordination and risk management)
- **1x Database Specialist** (Migration script optimization)
- **1x Frontend Developer** (WordPress theme development)

### **Pre-Migration Requirements**

#### **Access and Credentials Needed:**
- AWS account access with appropriate IAM permissions
- Current database backup and access credentials
- SSH access to current Elastic Beanstalk instances
- CloudFront and S3 configuration details
- Domain and DNS management access

#### **Environment Setup:**
- Local development environment matching production
- Staging environment for migration testing
- Backup and monitoring systems
- New CI/CD pipeline setup

This analysis provides a realistic foundation for project planning and resource allocation based on the current infrastructure complexity and technical debt identified in the off-boarding documentation.

## Next Steps

1. Review and approve this migration plan
2. Set up AWS accounts and permissions
3. Create staging environment for testing
4. Schedule maintenance window for production migration
5. Execute migration plan with monitoring

## Support and Maintenance

### Monitoring Checklist
- [ ] CloudWatch alarms configured
- [ ] Log aggregation set up
- [ ] Performance monitoring active
- [ ] Security scanning enabled
- [ ] Backup verification scheduled

### Regular Maintenance Tasks
- Weekly: Review CloudWatch metrics and logs
- Monthly: Update security patches and dependencies
- Quarterly: Review and optimize costs
- Annually: Security audit and penetration testing
