# AWS Migration Guide for Bon Voyage CakePHP + WordPress

This guide outlines the simplest approaches to migrate the Bon Voyage CakePHP application and WordPress blog to AWS while maintaining PHP 7.2 compatibility and scalability.

## Current Architecture Overview

- **CakePHP Application**: PHP 7.2 with Apache, document root at `app/webroot`
- **WordPress Blog**: Integrated at `/blog` path, using Timber framework
- **Database**: MariaDB 10.11 (MySQL compatible)
- **Assets**: File uploads and resources stored locally
- **Current Deployment**: Elastic Beanstalk (existing `.ebextensions` configs found)

## PHP 7.2 Compatibility Analysis

### AWS Platform Support Status
**⚠️ CRITICAL**: PHP 7.2 reached End of Life (EOL) on November 30, 2020

#### Current AWS Support:
- **Elastic Beanstalk**: PHP 7.2 platform deprecated (last supported version: PHP 7.2.34)
- **Amazon Linux 2**: PHP 7.2 available via `amazon-linux-extras` but deprecated
- **Amazon Linux 2023**: PHP 7.2 NOT available (minimum PHP 8.0)
- **Docker Images**: `php:7.2-apache` still available but no security updates

#### Recommended Approaches:

**Option A: Custom Docker with PHP 7.2 (Short-term)**
```dockerfile
# Use official PHP 7.2 image (frozen, no updates)
FROM php:7.2.34-apache

# Install security patches at OS level only
RUN apt-get update && apt-get upgrade -y \
    && apt-get install -y security-updates
```
- **Pros**: Maintains exact PHP 7.2 compatibility
- **Cons**: No PHP security updates, compliance risks
- **Timeline**: 3-6 months maximum before forced upgrade

**Option B: PHP 7.4 Migration (Recommended)**
```dockerfile
# Upgrade to PHP 7.4 (supported until Nov 2022, but more secure)
FROM php:7.4-apache
```
- **Pros**: Better security, AWS platform support
- **Cons**: Requires code compatibility testing
- **Timeline**: 2-4 weeks for testing and migration

**Option C: PHP 8.0+ Migration (Future-proof)**
```dockerfile
# Upgrade to PHP 8.1 (LTS until Nov 2024)
FROM php:8.1-apache
```
- **Pros**: Long-term support, performance improvements
- **Cons**: Significant code changes required
- **Timeline**: 4-8 weeks for full migration

#### PHP 7.2 Compatibility Testing Required:
```bash
# Test current codebase compatibility
composer require --dev phpcompatibility/php-compatibility
vendor/bin/phpcs --standard=PHPCompatibility --runtime-set testVersion 7.4 app/

# WordPress compatibility check
wp core check-update --allow-root
wp plugin list --update=available --format=table
```

### Docker Image Recommendations:

**For PHP 7.2 (Temporary Solution):**
```dockerfile
FROM php:7.2.34-apache
# Pin to specific version to avoid breaking changes
# Note: No security updates available
```

**For PHP 7.4 (Recommended Migration):**
```dockerfile
FROM php:7.4.33-apache
# Last version with security support
# Compatible with most PHP 7.2 code
```

### AWS Instance Compatibility:
- **EC2 Instances**: All current instance types support custom Docker images
- **Elastic Beanstalk**: Requires Docker platform (not PHP platform)
- **App Runner**: Full Docker support with any PHP version
- **ECS/Fargate**: Complete flexibility with Docker images

## Migration Options

### Option 1: AWS Elastic Beanstalk (Recommended - Existing Setup)

**Pros**: Already configured, familiar deployment process, auto-scaling, load balancing
**Cons**: Less control over infrastructure, potential vendor lock-in

#### Current Beanstalk Configuration
- Platform: PHP 7.2 on Amazon Linux
- Region: eu-west-2 (London)
- Environment: Bvwww-env-1
- EFS integration for shared file storage
- Automated deployments via GitLab CI

#### Migration Steps:
1. **Update Environment Variables**
   ```bash
   # Set production environment variables
   eb setenv WP_ENV=production \
            RDS_NAME=your-rds-database \
            RDS_USERNAME=your-db-user \
            RDS_PASSWORD=your-db-password \
            RDS_HOSTNAME=your-rds-endpoint \
            WP_HOME=https://www.bon-voyage.co.uk/blog \
            FORCE_SSL=true
   ```

2. **Database Migration**
   - Create RDS MySQL 5.7/8.0 instance
   - Export current database: `mysqldump bon-voyage > backup.sql`
   - Import to RDS: `mysql -h rds-endpoint -u username -p database_name < backup.sql`

3. **WordPress Media Storage Setup (S3 + EBS)**
   - S3 bucket for WordPress images: `s3://bon-voyage-wp-media`
   - EBS volume mounted to `/var/www/html/app/webroot/blog/app/uploads`
   - S3FS for transparent S3 mounting to EBS
   - Sync existing uploads: `aws s3 sync /local/uploads s3://bon-voyage-wp-media/uploads`

4. **Deploy**
   ```bash
   eb deploy Bvwww-env-1
   ```

### Option 2: AWS App Runner with Docker

**Pros**: Fully managed containers, automatic scaling, simpler than ECS
**Cons**: Newer service, less ecosystem integration

#### Setup Steps:
1. **Create Dockerfile** (based on existing `.docker/php/Dockerfile`)
2. **Configure App Runner**
   - Source: ECR or GitHub
   - Runtime: Docker
   - Auto-scaling: 1-10 instances
3. **Database**: RDS MySQL
4. **File Storage**: EFS or S3

### Option 3: Cloudways Managed Hosting

**Pros**: Managed PHP hosting, easy setup, built-in optimizations
**Cons**: Less AWS integration, potential performance limitations

#### Cloudways Setup:
1. **Create Server**
   - Provider: AWS
   - Server Size: 2GB+ RAM
   - PHP Version: 7.2
   - Location: London (eu-west-2)

2. **Application Setup**
   - Upload codebase via SFTP/Git
   - Configure database connection
   - Set up SSL certificate

## Recommended Migration Plan

### Phase 1: Infrastructure Setup (Day 1-2)
1. **RDS Database**
   ```bash
   # Create RDS MySQL instance
   aws rds create-db-instance \
     --db-instance-identifier bon-voyage-prod \
     --db-instance-class db.t3.micro \
     --engine mysql \
     --engine-version 8.0 \
     --allocated-storage 20 \
     --master-username admin \
     --master-user-password [secure-password] \
     --vpc-security-group-ids sg-xxxxxxxxx \
     --db-subnet-group-name default
   ```

2. **S3 Bucket for Assets**
   ```bash
   aws s3 mb s3://bon-voyage-assets-prod
   aws s3api put-bucket-policy --bucket bon-voyage-assets-prod --policy file://s3-policy.json
   ```

3. **CloudFront Distribution**
   - Origin: S3 bucket for assets
   - Alternate origin: Elastic Beanstalk environment
   - SSL Certificate: ACM certificate

### Phase 2: Application Configuration (Day 2-3)
1. **Update Environment Configuration**
   - Modify `app/config/.env` for production
   - Update WordPress URLs in `app/config/application.php`
   - Configure SSL redirects

2. **Database Migration**
   - Export development database
   - Import to RDS with production URLs
   - Update WordPress site URLs

3. **WordPress Media Storage Configuration (S3 + EBS)**
   - Create S3 bucket: `s3://bon-voyage-wp-media`
   - Install and configure S3FS on EC2 instances
   - Mount S3 bucket to EBS volume at `/var/www/html/app/webroot/blog/app/uploads`
   - Configure WordPress to use mounted storage path
   - Set up automatic sync and local caching for performance

### Phase 3: Deployment & Testing (Day 3-4)
1. **Deploy to Staging Environment**
   ```bash
   eb create staging-environment --platform "PHP 7.2"
   eb deploy staging-environment
   ```

2. **Testing Checklist**
   - [ ] CakePHP application loads correctly
   - [ ] WordPress blog accessible at `/blog`
   - [ ] Database connections working
   - [ ] File uploads functional
   - [ ] SSL certificates valid
   - [ ] Navigation between main site and blog works

3. **Production Deployment**
   ```bash
   eb deploy Bvwww-env-1
   ```

### Phase 4: DNS & CDN Setup (Day 4-5)
1. **CloudFlare Configuration**
   - Update A records to point to Elastic Beanstalk
   - Configure SSL/TLS settings
   - Set up page rules for caching
   - Enable security features

2. **Performance Optimization**
   - Configure CloudFront for static assets
   - Enable Gzip compression
   - Set up database connection pooling

## Environment Variables Required

```bash
# Database
RDS_NAME=bon_voyage_prod
RDS_USERNAME=admin
RDS_PASSWORD=secure_password_here
RDS_HOSTNAME=bon-voyage-prod.xxxxxxxxx.eu-west-2.rds.amazonaws.com

# WordPress
WP_ENV=production
WP_HOME=https://www.bon-voyage.co.uk/blog
WP_SITEURL=https://www.bon-voyage.co.uk/blog/wp
FORCE_SSL=true

# Security Keys (generate at https://api.wordpress.org/secret-key/1.1/salt/)
AUTH_KEY=generate_unique_key
SECURE_AUTH_KEY=generate_unique_key
LOGGED_IN_KEY=generate_unique_key
NONCE_KEY=generate_unique_key
AUTH_SALT=generate_unique_salt
SECURE_AUTH_SALT=generate_unique_salt
LOGGED_IN_SALT=generate_unique_salt
NONCE_SALT=generate_unique_salt

# AWS
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_DEFAULT_REGION=eu-west-2
```

## Scaling Configuration

### Auto Scaling Settings
```yaml
# .ebextensions/autoscaling.config
option_settings:
  aws:autoscaling:asg:
    MinSize: 2
    MaxSize: 10
  aws:autoscaling:trigger:
    MeasureName: CPUUtilization
    Unit: Percent
    UpperThreshold: 80
    LowerThreshold: 20
```

### Load Balancer Health Checks
- Health check URL: `/health-check.php`
- Healthy threshold: 2
- Unhealthy threshold: 5
- Timeout: 5 seconds
- Interval: 30 seconds

## Monitoring & Maintenance

1. **CloudWatch Alarms**
   - CPU utilization > 80%
   - Database connections > 80%
   - HTTP 5xx errors > 10/minute

2. **Backup Strategy**
   - RDS automated backups (7 days retention)
   - S3 versioning for uploaded files
   - Daily database snapshots

3. **Security**
   - WAF rules for common attacks
   - Security groups restricting database access
   - SSL/TLS encryption in transit and at rest

## Cost Estimation (Monthly)

- **Elastic Beanstalk**: $0 (service fee)
- **EC2 Instances**: ~$50-200 (depending on size/quantity)
- **RDS Database**: ~$25-100 (depending on instance size)
- **S3 Storage**: ~$5-20 (depending on usage)
- **CloudFront**: ~$10-50 (depending on traffic)
- **Total**: ~$90-370/month

## Rollback Plan

1. **DNS Rollback**: Update CloudFlare A records to previous hosting
2. **Database Rollback**: Restore from RDS snapshot
3. **Code Rollback**: `eb deploy` previous version
4. **File Rollback**: Restore from S3 versioned backups

## Docker Configuration for AWS

### Optimized Dockerfile for Production
```dockerfile
FROM php:7.2-apache

# Environment variables
ENV COMPOSER_BRANCH=1
ENV PHP_EXTENSIONS="soap gd mysqli pdo_mysql opcache"
ENV APACHE_DOCUMENT_ROOT=/var/www/html/app/webroot

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git zip unzip curl \
    libfreetype6-dev libjpeg62-turbo-dev libpng-dev \
    && rm -rf /var/lib/apt/lists/*

# Install PHP extensions
RUN docker-php-ext-configure gd --with-freetype-dir=/usr/include/ --with-jpeg-dir=/usr/include/ \
    && docker-php-ext-install -j$(nproc) gd mysqli pdo_mysql soap opcache

# Enable production PHP settings
RUN mv "$PHP_INI_DIR/php.ini-production" "$PHP_INI_DIR/php.ini"

# Configure OPcache for production
RUN echo "opcache.enable=1" >> /usr/local/etc/php/conf.d/opcache.ini \
    && echo "opcache.memory_consumption=256" >> /usr/local/etc/php/conf.d/opcache.ini \
    && echo "opcache.max_accelerated_files=20000" >> /usr/local/etc/php/conf.d/opcache.ini

# Install Composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer --1

# Configure Apache
RUN a2enmod rewrite \
    && sed -ri -e 's!/var/www/html!${APACHE_DOCUMENT_ROOT}!g' /etc/apache2/sites-available/*.conf \
    && sed -ri -e 's!/var/www/!${APACHE_DOCUMENT_ROOT}!g' /etc/apache2/apache2.conf /etc/apache2/conf-available/*.conf

COPY . /var/www/html/
WORKDIR /var/www/html

# Install dependencies and optimize
RUN composer install --no-dev --optimize-autoloader \
    && chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html

EXPOSE 80
```

### Docker Compose for Local Testing
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8080:80"
    environment:
      - RDS_NAME=bon_voyage
      - RDS_USERNAME=root
      - RDS_PASSWORD=password
      - RDS_HOSTNAME=db
      - WP_ENV=development
    depends_on:
      - db
    volumes:
      - ./app/webroot/blog/app/uploads:/var/www/html/app/webroot/blog/app/uploads

  db:
    image: mysql:5.7
    environment:
      MYSQL_DATABASE: bon_voyage
      MYSQL_ROOT_PASSWORD: password
    ports:
      - "3306:3306"
    volumes:
      - db_data:/var/lib/mysql

volumes:
  db_data:
```

## CloudFormation Templates

### RDS Database Stack
```yaml
# infrastructure/rds-stack.yaml
AWSTemplateFormatVersion: '2010-09-09'
Description: 'RDS MySQL database for Bon Voyage'

Parameters:
  DBInstanceClass:
    Type: String
    Default: db.t3.micro
    Description: RDS instance class

  DBAllocatedStorage:
    Type: Number
    Default: 20
    Description: Database storage in GB

Resources:
  DBSubnetGroup:
    Type: AWS::RDS::DBSubnetGroup
    Properties:
      DBSubnetGroupDescription: Subnet group for RDS
      SubnetIds:
        - subnet-51b92838
        - subnet-756dbf0f
        - subnet-de37d792

  DBSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Security group for RDS
      VpcId: vpc-3f2dae57
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 3306
          ToPort: 3306
          SourceSecurityGroupId: !Ref AppSecurityGroup

  AppSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Security group for application
      VpcId: vpc-3f2dae57

  Database:
    Type: AWS::RDS::DBInstance
    Properties:
      DBInstanceIdentifier: bon-voyage-prod
      DBInstanceClass: !Ref DBInstanceClass
      Engine: mysql
      EngineVersion: '8.0'
      AllocatedStorage: !Ref DBAllocatedStorage
      MasterUsername: admin
      MasterUserPassword: !Ref DBPassword
      VPCSecurityGroups:
        - !Ref DBSecurityGroup
      DBSubnetGroupName: !Ref DBSubnetGroup
      BackupRetentionPeriod: 7
      MultiAZ: true
      StorageEncrypted: true

  DBPassword:
    Type: AWS::SSM::Parameter::Value<String>
    Default: /bon-voyage/db-password
    NoEcho: true

Outputs:
  DatabaseEndpoint:
    Description: RDS endpoint
    Value: !GetAtt Database.Endpoint.Address
    Export:
      Name: !Sub "${AWS::StackName}-DatabaseEndpoint"
```

## WordPress Media Storage: S3 + EBS Configuration

### Architecture Overview
WordPress images are stored in S3 but mounted to the application instance's EBS volume for transparent access. This provides:
- **Durability**: S3's 99.999999999% (11 9's) durability
- **Performance**: Local EBS caching for fast access
- **Scalability**: Unlimited S3 storage capacity
- **Cost Efficiency**: S3 storage costs vs EBS volume costs

### S3 Bucket Setup
```bash
# Create S3 bucket for WordPress media
aws s3 mb s3://bon-voyage-wp-media --region eu-west-2

# Configure bucket policy for web access
aws s3api put-bucket-policy --bucket bon-voyage-wp-media --policy '{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "PublicReadGetObject",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::bon-voyage-wp-media/*"
    }
  ]
}'

# Enable versioning for backup protection
aws s3api put-bucket-versioning --bucket bon-voyage-wp-media --versioning-configuration Status=Enabled

# Configure lifecycle policy for cost optimization
aws s3api put-bucket-lifecycle-configuration --bucket bon-voyage-wp-media --lifecycle-configuration '{
  "Rules": [
    {
      "ID": "MediaOptimization",
      "Status": "Enabled",
      "Transitions": [
        {
          "Days": 30,
          "StorageClass": "STANDARD_IA"
        },
        {
          "Days": 90,
          "StorageClass": "GLACIER"
        }
      ]
    }
  ]
}'
```

### S3FS Configuration for EBS Mounting

#### Install S3FS on EC2 Instances
```bash
# .ebextensions/s3fs-setup.config
packages:
  yum:
    fuse: []
    fuse-devel: []
    gcc-c++: []
    git: []
    libcurl-devel: []
    libxml2-devel: []
    openssl-devel: []

commands:
  01_install_s3fs:
    command: |
      cd /tmp
      git clone https://github.com/s3fs-fuse/s3fs-fuse.git
      cd s3fs-fuse
      ./autogen.sh
      ./configure
      make
      make install

  02_create_mount_point:
    command: |
      mkdir -p /var/www/html/app/webroot/blog/app/uploads
      chown www-data:www-data /var/www/html/app/webroot/blog/app/uploads

  03_create_credentials:
    command: |
      echo "${AWS_ACCESS_KEY_ID}:${AWS_SECRET_ACCESS_KEY}" > /etc/passwd-s3fs
      chmod 600 /etc/passwd-s3fs

  04_mount_s3_bucket:
    command: |
      s3fs bon-voyage-wp-media /var/www/html/app/webroot/blog/app/uploads \
        -o passwd_file=/etc/passwd-s3fs \
        -o url=https://s3.eu-west-2.amazonaws.com \
        -o use_cache=/tmp/s3fs-cache \
        -o allow_other \
        -o mp_umask=022 \
        -o uid=33 \
        -o gid=33

  05_add_to_fstab:
    command: |
      echo "s3fs#bon-voyage-wp-media /var/www/html/app/webroot/blog/app/uploads fuse _netdev,allow_other,use_cache=/tmp/s3fs-cache,passwd_file=/etc/passwd-s3fs,url=https://s3.eu-west-2.amazonaws.com,uid=33,gid=33,mp_umask=022 0 0" >> /etc/fstab
```

#### Alternative: Using AWS CLI Sync (Simpler Approach)
```bash
# .ebextensions/s3-sync.config
files:
  "/usr/local/bin/s3-sync-uploads.sh":
    mode: "000755"
    owner: root
    group: root
    content: |
      #!/bin/bash
      UPLOAD_DIR="/var/www/html/app/webroot/blog/app/uploads"
      S3_BUCKET="s3://bon-voyage-wp-media"

      # Sync uploads to S3 every 5 minutes
      while true; do
        aws s3 sync "$UPLOAD_DIR" "$S3_BUCKET" --delete --quiet
        sleep 300
      done

  "/etc/systemd/system/s3-sync.service":
    mode: "000644"
    owner: root
    group: root
    content: |
      [Unit]
      Description=S3 Upload Sync Service
      After=network.target

      [Service]
      Type=simple
      User=root
      ExecStart=/usr/local/bin/s3-sync-uploads.sh
      Restart=always
      RestartSec=10

      [Install]
      WantedBy=multi-user.target

commands:
  01_enable_s3_sync:
    command: |
      systemctl enable s3-sync.service
      systemctl start s3-sync.service
```

### WordPress Configuration for S3 Storage

#### Update wp-config.php
```php
// app/webroot/blog/wp-config.php additions
define('UPLOADS', 'app/uploads');
define('WP_CONTENT_URL', WP_HOME . '/app');

// Optional: Use S3 URLs directly for better performance
if (WP_ENV === 'production') {
    define('WP_CONTENT_URL', 'https://bon-voyage-wp-media.s3.eu-west-2.amazonaws.com');
}
```

#### Install WordPress S3 Plugin (Alternative)
```bash
# Install WP Offload Media plugin for better S3 integration
wp plugin install amazon-s3-and-cloudfront --activate --allow-root

# Configure plugin settings
wp option update tantan_wordpress_s3 '{
  "provider": "aws",
  "access-key-id": "'"$AWS_ACCESS_KEY_ID"'",
  "secret-access-key": "'"$AWS_SECRET_ACCESS_KEY"'",
  "bucket": "bon-voyage-wp-media",
  "region": "eu-west-2",
  "copy-to-s3": "1",
  "serve-from-s3": "1",
  "remove-local-file": "0"
}' --format=json --allow-root
```

### Performance Optimization

#### EBS Volume Configuration
```yaml
# .ebextensions/ebs-optimization.config
option_settings:
  aws:autoscaling:launchconfiguration:
    BlockDeviceMappings: /dev/xvda=:20:true:gp3:3000:125
    # 20GB GP3 volume with 3000 IOPS and 125 MB/s throughput
```

#### S3FS Caching Configuration
```bash
# Optimize S3FS for better performance
s3fs bon-voyage-wp-media /var/www/html/app/webroot/blog/app/uploads \
  -o passwd_file=/etc/passwd-s3fs \
  -o url=https://s3.eu-west-2.amazonaws.com \
  -o use_cache=/tmp/s3fs-cache \
  -o ensure_diskfree=1000 \
  -o parallel_count=10 \
  -o multipart_size=64 \
  -o max_stat_cache_size=100000 \
  -o stat_cache_expire=900 \
  -o enable_noobj_cache \
  -o allow_other
```

### Monitoring and Maintenance

#### CloudWatch Metrics for S3 Storage
```bash
# Monitor S3 bucket usage
aws cloudwatch put-metric-alarm \
  --alarm-name "S3-Storage-Usage" \
  --alarm-description "Monitor S3 storage usage" \
  --metric-name BucketSizeBytes \
  --namespace AWS/S3 \
  --statistic Average \
  --period 86400 \
  --threshold 10737418240 \
  --comparison-operator GreaterThanThreshold \
  --dimensions Name=BucketName,Value=bon-voyage-wp-media Name=StorageType,Value=StandardStorage
```

#### Backup and Sync Verification
```bash
# Daily verification script
#!/bin/bash
# /usr/local/bin/verify-s3-sync.sh

UPLOAD_DIR="/var/www/html/app/webroot/blog/app/uploads"
S3_BUCKET="s3://bon-voyage-wp-media"

# Count local files
LOCAL_COUNT=$(find "$UPLOAD_DIR" -type f | wc -l)

# Count S3 objects
S3_COUNT=$(aws s3 ls "$S3_BUCKET" --recursive | wc -l)

# Log discrepancies
if [ "$LOCAL_COUNT" -ne "$S3_COUNT" ]; then
    echo "$(date): File count mismatch - Local: $LOCAL_COUNT, S3: $S3_COUNT" >> /var/log/s3-sync.log
    # Trigger sync
    aws s3 sync "$UPLOAD_DIR" "$S3_BUCKET" --delete
fi
```

### Cost Optimization

#### Storage Class Analysis
- **Standard**: First 30 days (frequent access)
- **Standard-IA**: 30-90 days (infrequent access)
- **Glacier**: 90+ days (archive)

#### Estimated Monthly Costs
```
S3 Storage (100GB):
- Standard (30 days): $2.30
- Standard-IA (60 days): $1.25
- Glacier (archive): $0.40
- Total S3: ~$4.00/month

EBS Volume (20GB GP3): ~$2.00/month
Data Transfer: ~$1.00/month

Total Storage Cost: ~$7.00/month
```

## Security Considerations

### IAM Roles and Policies
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject"
      ],
      "Resource": "arn:aws:s3:::bon-voyage-assets-prod/*"
    },
    {
      "Effect": "Allow",
      "Action": [
        "s3:ListBucket"
      ],
      "Resource": "arn:aws:s3:::bon-voyage-assets-prod"
    },
    {
      "Effect": "Allow",
      "Action": [
        "rds:DescribeDBInstances"
      ],
      "Resource": "*"
    }
  ]
}
```

### Security Headers Configuration
```apache
# .htaccess additions for security
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
Header always set Referrer-Policy "strict-origin-when-cross-origin"
Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'"
```

## Performance Optimization

### PHP Configuration for Production
```ini
; php.ini optimizations
memory_limit = 256M
max_execution_time = 30
max_input_time = 60
post_max_size = 32M
upload_max_filesize = 32M
max_file_uploads = 20

; OPcache settings
opcache.enable = 1
opcache.memory_consumption = 256
opcache.interned_strings_buffer = 16
opcache.max_accelerated_files = 20000
opcache.revalidate_freq = 2
opcache.fast_shutdown = 1
```

### Database Optimization
```sql
-- MySQL configuration recommendations
SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB for t3.micro
SET GLOBAL query_cache_size = 67108864; -- 64MB
SET GLOBAL query_cache_type = 1;
SET GLOBAL slow_query_log = 1;
SET GLOBAL long_query_time = 2;
```

## Troubleshooting Guide

### Common Issues and Solutions

1. **Database Connection Errors**
   ```bash
   # Check RDS security groups
   aws ec2 describe-security-groups --group-ids sg-xxxxxxxxx

   # Test database connection
   mysql -h your-rds-endpoint -u admin -p
   ```

2. **File Upload Issues**
   ```bash
   # Check EFS mount
   df -h | grep efs

   # Test file permissions
   ls -la /resources/
   ```

3. **SSL Certificate Problems**
   ```bash
   # Check certificate status
   aws acm list-certificates --region eu-west-2

   # Verify CloudFlare SSL settings
   curl -I https://www.bon-voyage.co.uk
   ```

4. **Performance Issues**
   ```bash
   # Check CloudWatch metrics
   aws cloudwatch get-metric-statistics \
     --namespace AWS/ApplicationELB \
     --metric-name TargetResponseTime \
     --start-time 2024-01-01T00:00:00Z \
     --end-time 2024-01-02T00:00:00Z \
     --period 3600 \
     --statistics Average
   ```

## Next Steps

1. Review and approve this migration plan
2. Set up AWS accounts and permissions
3. Create staging environment for testing
4. Schedule maintenance window for production migration
5. Execute migration plan with monitoring

## Support and Maintenance

### Monitoring Checklist
- [ ] CloudWatch alarms configured
- [ ] Log aggregation set up
- [ ] Performance monitoring active
- [ ] Security scanning enabled
- [ ] Backup verification scheduled

### Regular Maintenance Tasks
- Weekly: Review CloudWatch metrics and logs
- Monthly: Update security patches and dependencies
- Quarterly: Review and optimize costs
- Annually: Security audit and penetration testing
