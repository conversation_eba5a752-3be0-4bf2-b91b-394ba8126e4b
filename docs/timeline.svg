<svg aria-roledescription="gantt" role="graphics-document document" style="max-width: 1017px;" viewBox="0 0 1017 460" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-1750355888102"><style>#mermaid-1750355888102{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}#mermaid-1750355888102 .error-icon{fill:#552222;}#mermaid-1750355888102 .error-text{fill:#552222;stroke:#552222;}#mermaid-1750355888102 .edge-thickness-normal{stroke-width:1px;}#mermaid-1750355888102 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-1750355888102 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-1750355888102 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-1750355888102 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-1750355888102 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-1750355888102 .marker{fill:#333333;stroke:#333333;}#mermaid-1750355888102 .marker.cross{stroke:#333333;}#mermaid-1750355888102 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-1750355888102 p{margin:0;}#mermaid-1750355888102 .mermaid-main-font{font-family:var(--mermaid-font-family, "trebuchet ms", verdana, arial, sans-serif);}#mermaid-1750355888102 .exclude-range{fill:#eeeeee;}#mermaid-1750355888102 .section{stroke:none;opacity:0.2;}#mermaid-1750355888102 .section0{fill:rgba(102, 102, 255, 0.49);}#mermaid-1750355888102 .section2{fill:#fff400;}#mermaid-1750355888102 .section1,#mermaid-1750355888102 .section3{fill:white;opacity:0.2;}#mermaid-1750355888102 .sectionTitle0{fill:#333;}#mermaid-1750355888102 .sectionTitle1{fill:#333;}#mermaid-1750355888102 .sectionTitle2{fill:#333;}#mermaid-1750355888102 .sectionTitle3{fill:#333;}#mermaid-1750355888102 .sectionTitle{text-anchor:start;font-family:var(--mermaid-font-family, "trebuchet ms", verdana, arial, sans-serif);}#mermaid-1750355888102 .grid .tick{stroke:lightgrey;opacity:0.8;shape-rendering:crispEdges;}#mermaid-1750355888102 .grid .tick text{font-family:"trebuchet ms",verdana,arial,sans-serif;fill:#333;}#mermaid-1750355888102 .grid path{stroke-width:0;}#mermaid-1750355888102 .today{fill:none;stroke:red;stroke-width:2px;}#mermaid-1750355888102 .task{stroke-width:2;}#mermaid-1750355888102 .taskText{text-anchor:middle;font-family:var(--mermaid-font-family, "trebuchet ms", verdana, arial, sans-serif);}#mermaid-1750355888102 .taskTextOutsideRight{fill:black;text-anchor:start;font-family:var(--mermaid-font-family, "trebuchet ms", verdana, arial, sans-serif);}#mermaid-1750355888102 .taskTextOutsideLeft{fill:black;text-anchor:end;}#mermaid-1750355888102 .task.clickable{cursor:pointer;}#mermaid-1750355888102 .taskText.clickable{cursor:pointer;fill:#003163!important;font-weight:bold;}#mermaid-1750355888102 .taskTextOutsideLeft.clickable{cursor:pointer;fill:#003163!important;font-weight:bold;}#mermaid-1750355888102 .taskTextOutsideRight.clickable{cursor:pointer;fill:#003163!important;font-weight:bold;}#mermaid-1750355888102 .taskText0,#mermaid-1750355888102 .taskText1,#mermaid-1750355888102 .taskText2,#mermaid-1750355888102 .taskText3{fill:white;}#mermaid-1750355888102 .task0,#mermaid-1750355888102 .task1,#mermaid-1750355888102 .task2,#mermaid-1750355888102 .task3{fill:#8a90dd;stroke:#534fbc;}#mermaid-1750355888102 .taskTextOutside0,#mermaid-1750355888102 .taskTextOutside2{fill:black;}#mermaid-1750355888102 .taskTextOutside1,#mermaid-1750355888102 .taskTextOutside3{fill:black;}#mermaid-1750355888102 .active0,#mermaid-1750355888102 .active1,#mermaid-1750355888102 .active2,#mermaid-1750355888102 .active3{fill:#bfc7ff;stroke:#534fbc;}#mermaid-1750355888102 .activeText0,#mermaid-1750355888102 .activeText1,#mermaid-1750355888102 .activeText2,#mermaid-1750355888102 .activeText3{fill:black!important;}#mermaid-1750355888102 .done0,#mermaid-1750355888102 .done1,#mermaid-1750355888102 .done2,#mermaid-1750355888102 .done3{stroke:grey;fill:lightgrey;stroke-width:2;}#mermaid-1750355888102 .doneText0,#mermaid-1750355888102 .doneText1,#mermaid-1750355888102 .doneText2,#mermaid-1750355888102 .doneText3{fill:black!important;}#mermaid-1750355888102 .crit0,#mermaid-1750355888102 .crit1,#mermaid-1750355888102 .crit2,#mermaid-1750355888102 .crit3{stroke:#ff8888;fill:red;stroke-width:2;}#mermaid-1750355888102 .activeCrit0,#mermaid-1750355888102 .activeCrit1,#mermaid-1750355888102 .activeCrit2,#mermaid-1750355888102 .activeCrit3{stroke:#ff8888;fill:#bfc7ff;stroke-width:2;}#mermaid-1750355888102 .doneCrit0,#mermaid-1750355888102 .doneCrit1,#mermaid-1750355888102 .doneCrit2,#mermaid-1750355888102 .doneCrit3{stroke:#ff8888;fill:lightgrey;stroke-width:2;cursor:pointer;shape-rendering:crispEdges;}#mermaid-1750355888102 .milestone{transform:rotate(45deg) scale(0.8,0.8);}#mermaid-1750355888102 .milestoneText{font-style:italic;}#mermaid-1750355888102 .doneCritText0,#mermaid-1750355888102 .doneCritText1,#mermaid-1750355888102 .doneCritText2,#mermaid-1750355888102 .doneCritText3{fill:black!important;}#mermaid-1750355888102 .activeCritText0,#mermaid-1750355888102 .activeCritText1,#mermaid-1750355888102 .activeCritText2,#mermaid-1750355888102 .activeCritText3{fill:black!important;}#mermaid-1750355888102 .titleText{text-anchor:middle;font-size:18px;fill:#333;font-family:var(--mermaid-font-family, "trebuchet ms", verdana, arial, sans-serif);}#mermaid-1750355888102 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g></g><g text-anchor="middle" font-family="sans-serif" font-size="10" fill="none" transform="translate(175, 410)" class="grid"><path d="M0,-375V0H767V-375" stroke="currentColor" class="domain"></path><g transform="translate(47,0)" opacity="1" class="tick"><line y2="-375" stroke="currentColor"></line><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">Week 01</text></g><g transform="translate(102,0)" opacity="1" class="tick"><line y2="-375" stroke="currentColor"></line><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">Week 02</text></g><g transform="translate(157,0)" opacity="1" class="tick"><line y2="-375" stroke="currentColor"></line><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">Week 03</text></g><g transform="translate(211,0)" opacity="1" class="tick"><line y2="-375" stroke="currentColor"></line><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">Week 04</text></g><g transform="translate(266,0)" opacity="1" class="tick"><line y2="-375" stroke="currentColor"></line><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">Week 05</text></g><g transform="translate(321,0)" opacity="1" class="tick"><line y2="-375" stroke="currentColor"></line><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">Week 06</text></g><g transform="translate(376,0)" opacity="1" class="tick"><line y2="-375" stroke="currentColor"></line><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">Week 07</text></g><g transform="translate(431,0)" opacity="1" class="tick"><line y2="-375" stroke="currentColor"></line><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">Week 08</text></g><g transform="translate(485,0)" opacity="1" class="tick"><line y2="-375" stroke="currentColor"></line><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">Week 09</text></g><g transform="translate(540,0)" opacity="1" class="tick"><line y2="-375" stroke="currentColor"></line><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">Week 10</text></g><g transform="translate(595,0)" opacity="1" class="tick"><line y2="-375" stroke="currentColor"></line><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">Week 11</text></g><g transform="translate(650,0)" opacity="1" class="tick"><line y2="-375" stroke="currentColor"></line><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">Week 12</text></g><g transform="translate(705,0)" opacity="1" class="tick"><line y2="-375" stroke="currentColor"></line><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">Week 13</text></g><g transform="translate(759,0)" opacity="1" class="tick"><line y2="-375" stroke="currentColor"></line><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">Week 14</text></g></g><g><rect class="section section0" height="40" width="979.5" y="48" x="0"></rect><rect class="section section1" height="40" width="979.5" y="88" x="0"></rect><rect class="section section2" height="40" width="979.5" y="128" x="0"></rect><rect class="section section2" height="40" width="979.5" y="168" x="0"></rect><rect class="section section2" height="40" width="979.5" y="208" x="0"></rect><rect class="section section3" height="40" width="979.5" y="248" x="0"></rect><rect class="section section3" height="40" width="979.5" y="288" x="0"></rect><rect class="section section3" height="40" width="979.5" y="328" x="0"></rect><rect class="section section0" height="40" width="979.5" y="368" x="0"></rect></g><g><rect class="task task0" transform-origin="202.5px 65px" height="30" width="55" y="50" x="175" ry="3" rx="3" id="discovery"></rect><rect class="task task1" transform-origin="284.5px 105px" height="30" width="109" y="90" x="230" ry="3" rx="3" id="aws-setup"></rect><rect class="task task2" transform-origin="312px 145px" height="30" width="164" y="130" x="230" ry="3" rx="3" id="wp-setup"></rect><rect class="task task2" transform-origin="476.5px 185px" height="30" width="165" y="170" x="394" ry="3" rx="3" id="migration"></rect><rect class="task task2" transform-origin="613.5px 225px" height="30" width="109" y="210" x="559" ry="3" rx="3" id="integration"></rect><rect class="task task3" transform-origin="695.5px 265px" height="30" width="55" y="250" x="668" ry="3" rx="3" id="testing"></rect><rect class="task task3" transform-origin="750.5px 305px" height="30" width="55" y="290" x="723" ry="3" rx="3" id="staff-test"></rect><rect class="task task3" transform-origin="832.5px 345px" height="30" width="109" y="330" x="778" ry="3" rx="3" id="bugfix"></rect><rect class="task task0" transform-origin="914.5px 385px" height="30" width="55" y="370" x="887" ry="3" rx="3" id="deploy"></rect><text class="taskTextOutsideRight taskTextOutside0  width-143.40625" y="68.5" x="235" font-size="11" id="discovery-text">Site Analysis &amp; Requirements    </text><text class="taskText taskText1  width-93.234375" y="108.5" x="284.5" font-size="11" id="aws-setup-text">Environment Setup              </text><text class="taskText taskText2  width-82.109375" y="148.5" x="312" font-size="11" id="wp-setup-text">WordPress Setup        </text><text class="taskText taskText2  width-88.46875" y="188.5" x="476.5" font-size="11" id="migration-text">Content Migration             </text><text class="taskTextOutsideRight taskTextOutside2  width-122.65625" y="228.5" x="673" font-size="11" id="integration-text">Integration Development       </text><text class="taskTextOutsideRight taskTextOutside3  width-77.9375" y="268.5" x="728" font-size="11" id="testing-text">Internal Testing              </text><text class="taskTextOutsideRight taskTextOutside3  width-123.953125" y="308.5" x="783" font-size="11" id="staff-test-text">Staff Testing &amp; Feedback      </text><text class="taskTextOutsideLeft taskTextOutside3" y="348.5" x="773" font-size="11" id="bugfix-text">Bug Fixes &amp; Refinement       </text><text class="taskTextOutsideLeft taskTextOutside0" y="388.5" x="882" font-size="11" id="deploy-text">Final Deployment             </text></g><g><text class="sectionTitle sectionTitle0" font-size="11" y="70" x="10" dy="0em"><tspan x="10" alignment-baseline="central">Discovery &amp; Planning</tspan></text><text class="sectionTitle sectionTitle1" font-size="11" y="110" x="10" dy="0em"><tspan x="10" alignment-baseline="central">AWS Infrastructure</tspan></text><text class="sectionTitle sectionTitle2" font-size="11" y="190" x="10" dy="0em"><tspan x="10" alignment-baseline="central">WordPress Development</tspan></text><text class="sectionTitle sectionTitle3" font-size="11" y="310" x="10" dy="0em"><tspan x="10" alignment-baseline="central">Testing &amp; Refinement</tspan></text><text class="sectionTitle sectionTitle0" font-size="11" y="390" x="10" dy="0em"><tspan x="10" alignment-baseline="central">Go-Live</tspan></text></g><g class="today"><line class="today" y2="435" y1="25" x2="4370" x1="4370"></line></g><text class="titleText" y="25" x="508.5"></text></svg>

<style>
/* Mermaid diagram styling - match document theme */
.mermaid {
    font-family: "Avenir Next", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
    background: transparent !important;
}

/* Gantt chart - zebra striped row groups (project phases) */
.mermaid .section0 {
    fill: transparent !important;
}

.mermaid .section1 {
    fill: rgba(0, 0, 0, 0.1) !important; /* 10% grey */
}

.mermaid .section2 {
    fill: transparent !important;
}

.mermaid .section3 {
    fill: rgba(0, 0, 0, 0.1) !important; /* 10% grey */
}

.mermaid .section4 {
    fill: transparent !important;
}

/* Widen first column for row group labels */
.mermaid .sectionTitle {
    font-weight: 600 !important;
    text-anchor: start !important;
}

/* Remove row divider lines */
.mermaid .grid .tick line {
    display: none !important;
}

.mermaid .grid line {
    stroke: none !important;
}

/* Red task bars - no border, white text on bars */
.mermaid .task,
.mermaid .active0,
.mermaid .active1,
.mermaid .active2,
.mermaid .active3 {
    fill: #dc3545 !important;
    stroke: none !important;
    border: none !important;
}

/* White text on red bars */
.mermaid .taskText {
    fill: white !important;
    font-weight: 500 !important;
}

/* Keep text outside bars as normal */
.mermaid .taskTextOutsideRight,
.mermaid .taskTextOutsideLeft {
    fill: #333 !important;
}

/* General Mermaid text styling */
.mermaid text {
    font-family: "Avenir Next", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
}

/* Remove any default shadows or effects */
.mermaid * {
    filter: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
}

/* Widen first column for phase labels */
.mermaid .gantt .sectionTitle {
    font-weight: 600 !important;
    text-anchor: start !important;
    x: 20 !important; /* Move labels further left */
}

.mermaid .gantt .section {
    width: 200px !important; /* Widen section column */
}

/* Reinstate vertical week divider lines in grey */
.mermaid .grid .tick line {
    display: block !important;
    stroke: #ccc !important;
    stroke-width: 1px !important;
}

.mermaid .grid line {
    stroke: #ccc !important;
    stroke-width: 1px !important;
}

/* Make grey rows 20% grey instead of 10% */
.mermaid .section1 {
    fill: rgba(0, 0, 0, 0.2) !important; /* 20% grey */
}

.mermaid .section3 {
    fill: rgba(0, 0, 0, 0.2) !important; /* 20% grey */
}

/* Fix section title and task area overlap */
.mermaid .sectionTitle {
    font-weight: 600 !important;
    text-anchor: start !important;
    padding-right: 5px !important;
}

</style>
