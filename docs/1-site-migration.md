# Bon Voyage Migration

## Table of Contents


1. [Project Overview](#project-overview)
2. [Current System Analysis](#current-system-analysis)
3. [Migration Strategy](#migration-strategy)
4. [Timeline and Resource Allocation](#timeline-and-resource-allocation)
5. [Risk Assessment](#risk-assessment)
6. [Cost Analysis](#cost-analysis)
7. [Recommendations](#recommendations)


---

## 1. Project Overview

### **Migration Objective**
Complete modernization from legacy CakePHP 1.2.12 to WordPress + ACF + Gravity Forms. Addresses security vulnerabilities and platform end-of-life issues.

### **Approach**
Dual workstream with <PERSON> (Wordpress) and <PERSON> (AWS infrastructure) working in parallel.

### **Key Deliverables**
- Modern WordPress application with all existing functionality
- Database / content migration
- AWS infrastructure modernization (PHP 8.1, MySQL 8.0)
- Blog migration from Bedrock/Timber to vanilla WordPress
- Testing and documentation

<div style="page-break-before: always;"></div>

## 2. Current System Analysis

### **Critical Infrastructure Risks**

#### **Platform End-of-Life Issues**
1. **PHP 7.2**: No longer supported by AWS Elastic Beanstalk
2. **CakePHP 1.2.12**: No security updates since 2012
3. **Amazon Linux AMI**: Legacy images may be removed within 2 years

#### **Current Environment**
- **Platform**: PHP 7.2 on Amazon Linux (end-of-life)
- **Database**: MariaDB 10.11 with complex schema
- **Storage**: EFS and S3 bucket integration
- **CDN**: CloudFront for assets and resources
- **Blog**: WordPress with Bedrock/Timber framework

### **Database Complexity**
- **65+ tables** with 90,000+ records across CakePHP and WordPress systems
- **Complex relationships**: Hierarchical destinations, many-to-many associations, landing page integrations
- **Largest tables**: quote_requests (51,484), images (16,182), contacts (8,402), content_blocks (6,410)
- **Dual system**: Both CakePHP core tables and WordPress (wp_*) tables requiring careful migration planning

### **Application Functionality**
- **Content Management**: Hierarchical destinations, accommodation, activities
- **Quote Request System**: Contact forms with CRM integration
- **Third-Party Integrations**: Feefo reviews, Google APIs, ReCaptcha
- **Admin System**: ACL-based permissions with role management
- **Blog Integration**: Bedrock/Timber WordPress at /blog path

<div style="page-break-before: always;"></div>

## 3. Migration Strategy

### **Recommended Approach: WordPress + ACF Migration**

#### **Why WordPress + ACF (vs Direct CakePHP Migration)**
| Criteria | CakePHP Migration | WordPress + ACF |
|----------|-------------------|-----------------|
| **Development Time** | 6-12 months | 3-4 months |
| **Technical Risk** | <span class="emoji">❌</span> High | <span class="emoji">✅</span> Low |
| **Maintenance Cost** | <span class="emoji">❌</span> High | <span class="emoji">✅</span> Low |
| **Developer Availability** | <span class="emoji">❌</span> Limited | <span class="emoji">✅</span> High |
| **Future Scalability** | <span class="emoji">❌</span> Poor | <span class="emoji">✅</span> Excellent |
| **Security Updates** | <span class="emoji">❌</span> None | <span class="emoji">✅</span> Regular |

#### **CakePHP to WordPress Migration**
- **Content Structure**: CakePHP models → WordPress custom post types with ACF fields
- **Database Migration**: Automated scripts to transfer data while preserving relationships
- **URL Structure**: Maintain existing SEO-friendly URLs through WordPress rewrite rules
- **User Management**: CakePHP ACL system → WordPress roles and capabilities
- **Forms & Integrations**: Replace CakePHP components with WordPress plugins (Gravity Forms, etc.)
- **Template Conversion**: CakePHP views → WordPress themes with equivalent functionality

#### **Database Migration Feasibility: Highly Feasible**
- **Well-organized schema** with standard CMS patterns
- **WordPress flexibility** handles complex field structures via ACF
- **Comprehensive testing** will validate data integrity

#### **Blog Migration: Bedrock/Timber → Vanilla WordPress**
- **Current**: Complex Bedrock/Timber/Lumberjack stack
- **Target**: Standard WordPress with converted PHP templates
- **Benefits**: Easier maintenance, broader developer availability

<div style="page-break-before: always;"></div>

## 4. Timeline and Resource Allocation

### **Project Approach**
- **WordPress Development**: Jon handles application migration
- **AWS Infrastructure**: Ashley provides hosting environment
- **Development Effort**: 29-35 working days (non-consecutive)
- **Project Timeline**: 3-4 months calendar time
- **Testing**: 4-week dedicated testing and refinement phase



### **Project Timeline Overview**

```mermaid
%%{init: { "gantt": { "leftPadding": 175,"barHeight": 30, "barGap":10 } } }%%
gantt
    dateFormat YYYY-MM-DD
    axisFormat Week %U

    section Discovery & Planning
    Site Analysis & Requirements    :discovery, 2024-01-01, 1w

    section AWS Infrastructure
    Environment Setup              :aws-setup, after discovery, 2w

    section WordPress Development
    WordPress Setup        :wp-setup, after discovery, 3w
    Content Migration             :migration, after wp-setup, 3w
    Integration Development       :integration, after migration, 2w

    section Testing & Refinement
    Internal Testing              :testing, after integration, 1w
    Staff Testing & Feedback      :staff-test, after testing, 1w
    Bug Fixes & Refinement       :bugfix, after staff-test, 2w

    section Go-Live
    Final Deployment             :deploy, after bugfix, 1w
```

### **Project Phase Breakdown**
- **Discovery Phase**: Site analysis and conversion planning (1 week)
- **AWS Infrastructure**: Environment setup by Ashley (2 weeks, parallel)
- **WordPress Development**: Setup, migration, and integration (8 weeks)
- **Testing Phase**: Internal testing (1 week) + Staff testing (1 week) + Bug fixes (2 weeks)
- **Deployment**: Final go-live and support (1 week)
- **Total Duration**: 14 weeks (3-4 months calendar time)

<div style="page-break-before: always;"></div>

### **AWS Infrastructure Requirements**
The AWS work (Ashley) will provide:
- **Staging and production environments** with PHP 8.1 support
- **S3 storage integration** for WordPress media assets
- **Email sending capabilities** for forms and notifications
- **Automated backups** and disaster recovery procedures
- **Monitoring and alerting** for system health
- **Cloudflare integration** for CDN and security



<div style="page-break-before: always;"></div>

## 5. Risk Assessment

### **Technical Risks and Mitigation**

**Data Loss During Migration**
- **Probability**: Medium
- **Impact**: High
- **Mitigation**: Full database backup, incremental backups, rollback plan tested

**Integration Failures**
- **Probability**: Medium
- **Impact**: Medium
- **Mitigation**: Comprehensive testing phases, staging environment validation
- 
**Extended Downtime**
- **Probability**: Low-Medium
- **Impact**: High
- **Mitigation**: Parallel environment, DNS TTL reduction, <1 hour rollback

#### **Business Continuity**
- **Rollback Strategy**: Complete restoration under 1 hour
- **Backup Procedures**: Database, files, and configuration backups
- **Communication Plan**: Daily updates, weekly reports, emergency contacts

### **Success Factors**
- **WordPress Flexibility**: ACF handles complex field structures
- **Existing APIs**: WP-CLI, ACF Pro, Gravity Forms provide robust tools
- **Clean Database Design**: Well-normalized, standard patterns
- **Comprehensive Testing**: Dedicated phases for validation and refinement
- **Moderate Pace**: Realistic timeline with padding for technical challenges

<div style="page-break-before: always;"></div>

## 6. Cost Analysis

### **Development Costs**
| Component | Duration | Daily Rate | Total Cost |
|-----------|----------|------------|------------|
| **Discovery & Planning Phase** | 5 days | £375/day | £1,875 |
| **WordPress Development** | 16-20 days | £375/day | £6,000 - £7,500 |
| **Testing & Bug Fixes** | 8-10 days | £375/day | £3,000 - £3,750 |
| **Technical Challenges Buffer (15%)** | - | - | £1,631 - £1,969 |
| **Total Development Cost** | **29-35 days effort** | - | **£12,506 - £15,094** |

*Note: AWS infrastructure work performed in-house*

### **WordPress Plugin Licenses (Annual)**
| Plugin | Annual Cost | Purpose |
|--------|-------------|---------|
| **ACF Pro** | £49/year | Advanced custom fields for content structure |
| **Gravity Forms** | £59/year | Contact forms and quote requests |
| **WP DB Migrate Pro** | £49/year | Database migration and deployment |
| **WordFence Premium** | £149/year | Security monitoring and firewall protection |
| **Total Plugin Costs** | **£306/year** | **Essential functionality** |



<div style="page-break-before: always;"></div>

## 7. Recommendations

### **Primary Recommendation: WordPress Migration**

#### **Immediate Benefits**
- **Security**: Eliminates all EOL platform vulnerabilities
- **Maintainability**: Modern, well-supported platform
- **Cost-Effective**: Reasonable investment with long-term value
- **Future-Proof**: Regular updates and long-term support

#### **Implementation Approach**
1. **Discovery Phase**: Thorough analysis and conversion planning
2. **Coordinated Development**: AWS infrastructure + WordPress application
3. **Comprehensive Testing**: Multiple testing phases with staff involvement
4. **Gradual Deployment**: Staging → Staff Testing → Production
5. **Knowledge Transfer**: Documentation and training

### **Next Steps**
1. **Approve migration strategy** and budget allocation (£12,506 - £15,094)
2. **Confirm Jon's availability** for WordPress development work
3. **Schedule discovery phase** to begin site analysis and planning (1 week)
4. **Coordinate with Ashley** for infrastructure preparation
5. **Plan staff testing schedule** to ensure comprehensive validation (1 week)
